"use client";
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/app/components/ui/tabs";
import { PredictiveAnalytics } from './PredictiveAnalytics';
import { SmartAutomation } from './SmartAutomation';
import { SmartInsights } from './SmartInsights';
import { SentimentAnalysis } from './SentimentAnalysis';
import { Bot, Brain, Zap, TrendingUp } from 'lucide-react';
import { citizenService } from '@/services/citizenService';
import { demandService } from '@/services/demandService';
import { commitmentService } from '@/services/commitmentService';
import { socialPostService } from '@/services/socialPostService';

interface AIHubProps {
  darkMode: boolean;
}

export const AIHub = ({ darkMode }: AIHubProps) => {
  const [data, setData] = useState({
    analytics: {},
    citizens: [],
    requests: [],
    commitments: [],
    socialPosts: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAIData();
  }, []);

  const loadAIData = async () => {
    try {
      const [citizens, requests, commitments, socialPosts] = await Promise.all([
        citizenService.getAll().catch(() => []),
        demandService.getAll().catch(() => []),
        commitmentService.getAll().catch(() => []),
        socialPostService.getAll().catch(() => [])
      ]);

      // Calculate analytics for AI
      const analytics = {
        totalCitizens: citizens?.length || 0,
        totalRequests: requests?.length || 0,
        resolvedRequests: requests?.filter(r => r.status === 'resolvido').length || 0,
        totalCommitments: commitments?.length || 0,
        totalPosts: socialPosts?.length || 0
      };

      setData({
        analytics,
        citizens: citizens || [],
        requests: requests || [],
        commitments: commitments || [],
        socialPosts: socialPosts || []
      });
    } catch (error) {
      console.error('Error loading AI data:', error);
      // Definir valores padrão em caso de erro
      setData({
        analytics: {
          totalCitizens: 0,
          totalRequests: 0,
          resolvedRequests: 0,
          totalCommitments: 0,
          totalPosts: 0
        },
        citizens: [],
        requests: [],
        commitments: [],
        socialPosts: []
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={`text-center py-12 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p>Carregando IA Central...</p>
      </div>
    );
  }
  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-3">
        <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
          <Brain className="w-6 h-6 text-white" />
        </div>
        <div>
          <h1 className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
            Central de Inteligência Artificial
          </h1>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Análises avançadas e automação inteligente para seu gabinete
          </p>
        </div>
      </div>

      <Tabs defaultValue="insights" className="w-full">
        <TabsList className={`grid w-full grid-cols-4 ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100'}`}>
          <TabsTrigger value="insights" className="flex items-center space-x-2">
            <TrendingUp className="w-4 h-4" />
            <span>Insights</span>
          </TabsTrigger>
          <TabsTrigger value="predictive" className="flex items-center space-x-2">
            <Brain className="w-4 h-4" />
            <span>Preditiva</span>
          </TabsTrigger>
          <TabsTrigger value="automation" className="flex items-center space-x-2">
            <Zap className="w-4 h-4" />
            <span>Automação</span>
          </TabsTrigger>
          <TabsTrigger value="sentiment" className="flex items-center space-x-2">
            <Bot className="w-4 h-4" />
            <span>Sentimento</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="insights">
          <SmartInsights 
            darkMode={darkMode}
            analytics={data.analytics}
            citizens={data.citizens}
            requests={data.requests}
            commitments={data.commitments}
          />
        </TabsContent>

        <TabsContent value="predictive">
          <PredictiveAnalytics 
            darkMode={darkMode}
            analytics={data.analytics}
            requests={data.requests}
            citizens={data.citizens}
          />
        </TabsContent>

        <TabsContent value="automation">
          <SmartAutomation darkMode={darkMode} />
        </TabsContent>

        <TabsContent value="sentiment">
          <SentimentAnalysis 
            darkMode={darkMode}
            socialPosts={data.socialPosts}
            requests={data.requests}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};