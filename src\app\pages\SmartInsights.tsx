"use client";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { TrendingUp, AlertTriangle, Lightbulb, Target, Users, Calendar } from 'lucide-react';

interface SmartInsightsProps {
  darkMode: boolean;
  analytics: any;
  citizens: any[];
  requests: any[];
  commitments: any[];
}

interface Insight {
  id: string;
  type: 'trend' | 'alert' | 'suggestion' | 'opportunity';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  category: string;
  actionable: boolean;
  confidence: number;
}

export const SmartInsights = ({ darkMode, analytics, citizens, requests, commitments }: SmartInsightsProps) => {
  
  const generateInsights = (): Insight[] => {
    const insights: Insight[] = [];

    // Análise de demandas
    const totalRequests = requests?.length || 0;
    if (totalRequests > 0) {
      insights.push({
        id: '1',
        type: 'trend',
        title: 'Volume de Demandas',
        description: `${totalRequests} demandas registradas no sistema. ${totalRequests > 50 ? 'Alto volume requer atenção.' : 'Volume controlável.'}`,
        impact: totalRequests > 50 ? 'high' : 'medium',
        category: 'Operacional',
        actionable: totalRequests > 50,
        confidence: 100
      });
    }

    // Análise de cidadãos
    const totalCidadaos = citizens?.length || 0;
    if (totalCidadaos > 0) {
      insights.push({
        id: '2',
        type: 'opportunity',
        title: 'Base de Contatos',
        description: `${totalCidadaos} cidadãos cadastrados. ${totalCidadaos > 100 ? 'Excelente rede de contatos!' : 'Continue expandindo sua base.'}`,
        impact: totalCidadaos > 100 ? 'high' : 'medium',
        category: 'Político',
        actionable: totalCidadaos <= 100,
        confidence: 100
      });
    }

    // Análise de agenda
    const today = new Date().toISOString().split('T')[0];
    const compromissosHoje = commitments?.filter(c => c.date === today).length || 0;
    const totalCompromissos = commitments?.length || 0;
    
    if (totalCompromissos > 0) {
      insights.push({
        id: '3',
        type: compromissosHoje > 3 ? 'alert' : 'trend',
        title: compromissosHoje > 3 ? 'Agenda Sobrecarregada' : 'Agenda Organizada',
        description: `${compromissosHoje} compromissos hoje de ${totalCompromissos} total. ${compromissosHoje > 3 ? 'Considere reagendar alguns.' : 'Agenda bem distribuída.'}`,
        impact: compromissosHoje > 3 ? 'medium' : 'low',
        category: 'Agenda',
        actionable: compromissosHoje > 3,
        confidence: 100
      });
    }

    // Análise de demandas por área
    if (totalRequests > 0) {
      const topArea = Object.entries(analytics?.requestsByArea || {}).sort(([,a], [,b]) => (b as number) - (a as number))[0];
      if (topArea) {
        const [area, count] = topArea;
        const percentage = (((count as number) / totalRequests) * 100).toFixed(1);
        insights.push({
          id: '4',
          type: 'suggestion',
          title: `Área Dominante: ${area}`,
          description: `${percentage}% das demandas são sobre ${area.toLowerCase()}. Considere especializar atendimento nesta área.`,
          impact: (count as number) > totalRequests * 0.4 ? 'high' : 'medium',
          category: 'Estratégico',
          actionable: true,
          confidence: 100
        });
      }
    }

    // Análise de taxa de resolução
    const resolvedCount = requests?.filter(r => r.status === 'resolvido').length || 0;
    if (totalRequests > 0) {
      const resolutionRate = (resolvedCount / totalRequests) * 100;
      insights.push({
        id: '5',
        type: resolutionRate > 70 ? 'opportunity' : 'alert',
        title: resolutionRate > 70 ? 'Excelente Taxa de Resolução' : 'Taxa de Resolução Baixa',
        description: `${resolutionRate.toFixed(1)}% das demandas foram resolvidas (${resolvedCount}/${totalRequests}). ${resolutionRate > 70 ? 'Continue o bom trabalho!' : 'Foque em resolver mais demandas.'}`,
        impact: resolutionRate > 70 ? 'high' : 'medium',
        category: 'Operacional',
        actionable: resolutionRate <= 70,
        confidence: 100
      });
    }

    return insights;
  };

  const insights = generateInsights();

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'trend': return TrendingUp;
      case 'alert': return AlertTriangle;
      case 'suggestion': return Lightbulb;
      case 'opportunity': return Target;
      default: return Lightbulb;
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'trend': return 'blue';
      case 'alert': return 'red';
      case 'suggestion': return 'yellow';
      case 'opportunity': return 'green';
      default: return 'gray';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return darkMode ? 'text-red-400 bg-red-900/30' : 'text-red-600 bg-red-50';
      case 'medium': return darkMode ? 'text-yellow-400 bg-yellow-900/30' : 'text-yellow-600 bg-yellow-50';
      case 'low': return darkMode ? 'text-green-400 bg-green-900/30' : 'text-green-600 bg-green-50';
      default: return darkMode ? 'text-gray-400 bg-gray-700' : 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
      <CardHeader>
        <CardTitle className={`flex items-center space-x-2 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
          <Lightbulb className="w-5 h-5" />
          <span>Insights Inteligentes</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {insights.length === 0 ? (
          <p className={`text-center py-8 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Nenhum insight disponível no momento.
          </p>
        ) : (
          insights.map((insight) => {
            const Icon = getInsightIcon(insight.type);
            const color = getInsightColor(insight.type);
            
            return (
              <div
                key={insight.id}
                className={`p-4 rounded-lg border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <Icon className={`w-4 h-4 text-${color}-500`} />
                    <h4 className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                      {insight.title}
                    </h4>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={`text-xs ${getImpactColor(insight.impact)}`}>
                      {insight.impact}
                    </Badge>
                    <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      {insight.confidence}% confiança
                    </span>
                  </div>
                </div>
                
                <p className={`text-sm mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  {insight.description}
                </p>
                
                <div className="flex items-center justify-between">
                  <span className={`text-xs px-2 py-1 rounded ${darkMode ? 'bg-gray-600 text-gray-300' : 'bg-gray-200 text-gray-600'}`}>
                    {insight.category}
                  </span>
                  
                  {insight.actionable && (
                    <button className={`text-xs px-3 py-1 rounded-full border ${
                      darkMode 
                        ? 'border-blue-400 text-blue-400 hover:bg-blue-900/30' 
                        : 'border-blue-600 text-blue-600 hover:bg-blue-50'
                    }`}>
                      Ação Sugerida
                    </button>
                  )}
                </div>
              </div>
            );
          })
        )}
      </CardContent>
    </Card>
  );
};