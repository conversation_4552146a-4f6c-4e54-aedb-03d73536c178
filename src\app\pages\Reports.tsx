"use client";
import { useState, useEffect } from 'react';
import { Download, FileText, BarChart3, Calendar, Users, Filter } from 'lucide-react';
import { citizenService } from '@/services/citizenService';
import { demandService } from '@/services/demandService';
import { commitmentService } from '@/services/commitmentService';
import { projectService } from '@/services/projectService';

export const Reports = ({ darkMode }: { darkMode: boolean }) => {
    const [reportData, setReportData] = useState({
        citizens: [],
        demands: [],
        commitments: [],
        projects: []
    });
    const [loading, setLoading] = useState(true);
    const [selectedPeriod, setSelectedPeriod] = useState('month');
    const [selectedType, setSelectedType] = useState('all');

    useEffect(() => {
        loadReportData();
    }, []);

    const loadReportData = async () => {
        try {
            const [citizens, demands, commitments, projects] = await Promise.all([
                citizenService.getAll(),
                demandService.getAll(),
                commitmentService.getAll(),
                projectService.getAll()
            ]);

            setReportData({
                citizens: citizens || [],
                demands: demands || [],
                commitments: commitments || [],
                projects: projects || []
            });
        } catch (error) {
            console.error('Error loading report data:', error);
        } finally {
            setLoading(false);
        }
    };

    const generateReport = (type: string) => {
        const data = reportData[type as keyof typeof reportData];
        const csv = convertToCSV(data, type);
        downloadCSV(csv, `relatorio_${type}_${new Date().toISOString().split('T')[0]}.csv`);
    };

    const convertToCSV = (data: any[], type: string) => {
        if (!data.length) return '';
        
        const headers = Object.keys(data[0]).join(',');
        const rows = data.map(item => 
            Object.values(item).map(value => 
                typeof value === 'object' ? JSON.stringify(value) : value
            ).join(',')
        );
        
        return [headers, ...rows].join('\n');
    };

    const downloadCSV = (csv: string, filename: string) => {
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        window.URL.revokeObjectURL(url);
    };

    const getStats = () => {
        const now = new Date();
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        
        return {
            totalCitizens: reportData.citizens.length,
            totalDemands: reportData.demands.length,
            totalCommitments: reportData.commitments.length,
            totalProjects: reportData.projects.length,
            monthlyDemands: reportData.demands.filter(d => 
                new Date(d.created_at) >= monthStart
            ).length,
            resolvedDemands: reportData.demands.filter(d => 
                d.status === 'resolvido'
            ).length
        };
    };

    const stats = getStats();

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <h1 className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
                    Relatórios e Analytics
                </h1>
                <div className="flex space-x-2">
                    <select
                        value={selectedPeriod}
                        onChange={(e) => setSelectedPeriod(e.target.value)}
                        className={`px-4 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                    >
                        <option value="week">Última Semana</option>
                        <option value="month">Último Mês</option>
                        <option value="quarter">Último Trimestre</option>
                        <option value="year">Último Ano</option>
                    </select>
                </div>
            </div>

            {/* Estatísticas Gerais */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                    <div className="flex items-center justify-between">
                        <div>
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Cidadãos</p>
                            <p className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{stats.totalCitizens}</p>
                        </div>
                        <Users className={`w-8 h-8 ${darkMode ? 'text-blue-400' : 'text-blue-600'}`} />
                    </div>
                </div>

                <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                    <div className="flex items-center justify-between">
                        <div>
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Demandas</p>
                            <p className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{stats.totalDemands}</p>
                        </div>
                        <FileText className={`w-8 h-8 ${darkMode ? 'text-green-400' : 'text-green-600'}`} />
                    </div>
                </div>

                <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                    <div className="flex items-center justify-between">
                        <div>
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Compromissos</p>
                            <p className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{stats.totalCommitments}</p>
                        </div>
                        <Calendar className={`w-8 h-8 ${darkMode ? 'text-purple-400' : 'text-purple-600'}`} />
                    </div>
                </div>

                <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                    <div className="flex items-center justify-between">
                        <div>
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Taxa Resolução</p>
                            <p className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
                                {stats.totalDemands > 0 ? Math.round((stats.resolvedDemands / stats.totalDemands) * 100) : 0}%
                            </p>
                        </div>
                        <BarChart3 className={`w-8 h-8 ${darkMode ? 'text-orange-400' : 'text-orange-600'}`} />
                    </div>
                </div>
            </div>

            {/* Relatórios Disponíveis */}
            <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                <h2 className={`text-xl font-semibold mb-4 ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
                    Relatórios Disponíveis
                </h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className={`p-4 border rounded-lg ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                        <div className="flex items-center justify-between mb-2">
                            <h3 className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>Relatório de Cidadãos</h3>
                            <button
                                onClick={() => generateReport('citizens')}
                                className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center space-x-1"
                            >
                                <Download className="w-4 h-4" />
                                <span>Baixar</span>
                            </button>
                        </div>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Lista completa de cidadãos cadastrados com informações de contato
                        </p>
                    </div>

                    <div className={`p-4 border rounded-lg ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                        <div className="flex items-center justify-between mb-2">
                            <h3 className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>Relatório de Demandas</h3>
                            <button
                                onClick={() => generateReport('demands')}
                                className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 flex items-center space-x-1"
                            >
                                <Download className="w-4 h-4" />
                                <span>Baixar</span>
                            </button>
                        </div>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Todas as demandas por status, prioridade e responsável
                        </p>
                    </div>

                    <div className={`p-4 border rounded-lg ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                        <div className="flex items-center justify-between mb-2">
                            <h3 className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>Relatório de Agenda</h3>
                            <button
                                onClick={() => generateReport('commitments')}
                                className="bg-purple-600 text-white px-3 py-1 rounded text-sm hover:bg-purple-700 flex items-center space-x-1"
                            >
                                <Download className="w-4 h-4" />
                                <span>Baixar</span>
                            </button>
                        </div>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Compromissos agendados com datas, horários e status
                        </p>
                    </div>

                    <div className={`p-4 border rounded-lg ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                        <div className="flex items-center justify-between mb-2">
                            <h3 className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>Relatório de Projetos</h3>
                            <button
                                onClick={() => generateReport('projects')}
                                className="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700 flex items-center space-x-1"
                            >
                                <Download className="w-4 h-4" />
                                <span>Baixar</span>
                            </button>
                        </div>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                            Projetos em andamento com orçamentos e status
                        </p>
                    </div>
                </div>
            </div>

            {loading && (
                <div className={`text-center py-12 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p>Carregando dados dos relatórios...</p>
                </div>
            )}
        </div>
    );
};