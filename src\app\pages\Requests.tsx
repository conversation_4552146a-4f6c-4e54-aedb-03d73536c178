"use client";
import { useState, useEffect } from 'react';
import { Plus } from 'lucide-react';
import { NewRequestModal } from './NewRequestModal';
import { demandService } from '@/services/demandService';

export const Requests = ({ getPriorityColor, getStatusColor, darkMode }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [requests, setRequests] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        loadRequests();
    }, []);

    const loadRequests = async () => {
        try {
            const data = await demandService.getAll().catch(() => []);
            setRequests(data || []);
        } catch (error) {
            console.error('Error loading requests:', error);
            setRequests([]);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <h1 className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Demandas e Solicitações</h1>
                <button onClick={() => setIsModalOpen(true)} className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
                    <Plus className="w-4 h-4" />
                    <span>Nova Demanda</span>
                </button>
            </div>
            <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                {/* Filtros aqui */}
                <div className="space-y-4">
                    {loading ? (
                        <div className="text-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Carregando demandas...</p>
                        </div>
                    ) : requests.length > 0 ? (
                        requests.map(req => (
                        <div key={req.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                            <div className="flex items-start justify-between mb-2">
                                <div>
                                    <h3 className={`font-medium ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{req.subject}</h3>
                                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Solicitante: {req.citizen_name} | Responsável: {req.responsible_staff}</p>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(req.priority)}`}>{req.priority}</span>
                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(req.status)}`}>{req.status}</span>
                                </div>
                            </div>
                            <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>{req.description}</p>
                        </div>
                        ))
                    ) : (
                        <div className="text-center py-8">
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Nenhuma demanda encontrada</p>
                        </div>
                    )}
                </div>
            </div>
            <NewRequestModal isOpen={isModalOpen} setIsOpen={setIsModalOpen} darkMode={darkMode} onSave={loadRequests} />
        </div>
    );
};