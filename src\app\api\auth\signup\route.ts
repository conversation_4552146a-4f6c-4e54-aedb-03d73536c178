import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    const { email, password, name, role } = await request.json();

    // 1. Criar usuário no Supabase Auth (cadastro normal)
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: { name, role }
      }
    });

    if (authError) {
      return NextResponse.json({ error: authError.message }, { status: 400 });
    }

    // 2. Criar tenant (gabinete)
    const tenantSlug = name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
    
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .insert({
        name: `Gabinete ${name}`,
        slug: tenantSlug
      })
      .select()
      .single();

    if (tenantError) {
      return NextResponse.json({ error: tenantError.message }, { status: 400 });
    }

    // 3. Gerar API key para o tenant
    const apiKey = `gbc_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
    
    const { error: keyError } = await supabase
      .from('tenant_api_keys')
      .insert({
        tenant_id: tenant.id,
        api_key: apiKey
      });

    if (keyError) {
      return NextResponse.json({ error: keyError.message }, { status: 400 });
    }

    // 4. Criar contextos padrão
    const defaultContexts = [
      { page: 'dashboard', content: 'Contexto: Dashboard do gabinete. Foque em análises gerais e métricas.' },
      { page: 'citizens', content: 'Contexto: Gestão de cidadãos e relacionamento público.' },
      { page: 'requests', content: 'Contexto: Demandas e solicitações dos cidadãos.' },
      { page: 'agenda', content: 'Contexto: Agenda de compromissos e eventos.' },
      { page: 'projects', content: 'Contexto: Projetos e iniciativas do gabinete.' }
    ];

    for (const ctx of defaultContexts) {
      await supabase.from('contexts').insert({
        tenant_id: tenant.id,
        page: ctx.page,
        content: ctx.content
      });
    }

    // 5. Criar prompts padrão
    const defaultPrompts = [
      { page: 'dashboard', prompt: 'Você é um assistente especializado em gestão de gabinetes políticos brasileiros.' },
      { page: 'citizens', prompt: 'Você é um assistente para gestão de cidadãos e relacionamento público.' },
      { page: 'requests', prompt: 'Você é um assistente para demandas dos cidadãos.' },
      { page: 'agenda', prompt: 'Você é um assistente para agenda de compromissos.' },
      { page: 'projects', prompt: 'Você é um assistente para projetos e iniciativas.' }
    ];

    for (const prompt of defaultPrompts) {
      await supabase.from('prompt_templates').insert({
        tenant_id: tenant.id,
        page: prompt.page,
        system_prompt: prompt.prompt + ' Responda sempre em português brasileiro.'
      });
    }

    return NextResponse.json({
      message: 'Conta criada com sucesso! Verifique seu email para confirmar a conta antes de fazer login.',
      tenant: {
        id: tenant.id,
        name: tenant.name,
        slug: tenant.slug,
        api_key: apiKey
      }
    });

  } catch (error) {
    console.error('Erro no cadastro:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}