import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Páginas públicas que não precisam de autenticação
  const publicPaths = ['/login', '/register'];
  
  // Se está tentando acessar uma página pública, permite
  if (publicPaths.includes(pathname)) {
    return NextResponse.next();
  }
  
  // Para outras páginas, verifica se tem usuário no localStorage
  // Nota: Em middleware não temos acesso ao localStorage, então vamos deixar
  // o ProtectedRoute fazer essa verificação no lado do cliente
  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};