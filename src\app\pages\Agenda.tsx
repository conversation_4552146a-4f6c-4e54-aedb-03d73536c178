"use client";
import { useState, useEffect } from 'react';
import { Plus, Search, Filter } from 'lucide-react';
import { commitmentService } from '@/services/commitmentService';
import { CommitmentCard } from './CommitmentCard';
import { NewCommitmentModal } from './NewCommitmentModal';
import { Calendar } from './Calendar';
import { DayCommitmentsModal } from './DayCommitmentsModal';

export const Agenda = ({ setSelectedCommitment, getStatusColor, darkMode }: { setSelectedCommitment: (commitment: any) => void, getStatusColor: (status: string) => string, darkMode: boolean }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedStatus, setSelectedStatus] = useState('all');
    const [selectedDate, setSelectedDate] = useState('');
    const [commitments, setCommitments] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        loadCommitments();
    }, []);

    const loadCommitments = async () => {
        try {
            const data = await commitmentService.getAll();
            setCommitments(data || []);
        } catch (error) {
            console.error('Error loading commitments:', error);
        } finally {
            setLoading(false);
        }
    };
    const [selectedDay, setSelectedDay] = useState<{date: string, commitments: any[]} | null>(null);

    const statuses = ['all', 'confirmado', 'pendente', 'cancelado'];

    const filteredCommitments = commitments.filter(commitment => {
        const matchesSearch = commitment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            commitment.description?.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = selectedStatus === 'all' || commitment.status === selectedStatus;
        const matchesDate = !selectedDate || commitment.date === selectedDate;
        return matchesSearch && matchesStatus && matchesDate;
    });

    return (
      <div className="space-y-6">
        <div className="space-y-4">
          <h1
            className={`text-2xl font-bold ${
              darkMode ? "text-gray-100" : "text-gray-900"
            }`}
          >
            Agenda de Compromissos
          </h1>
          <div className="flex flex-col md:flex-row gap-4 items-end">
            <div className="relative flex-1">
              <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
              <input
                type="text"
                placeholder="Buscar compromissos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full pl-10 pr-4 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100 placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'}`}
              />
            </div>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className={`px-4 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
            >
              {statuses.map(status => (
                <option key={status} value={status}>{status === 'all' ? 'Todos os status' : status}</option>
              ))}
            </select>
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className={`px-4 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
            />
            <button
              onClick={() => setIsModalOpen(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2 whitespace-nowrap"
            >
              <Plus className="w-4 h-4" />
              <span>Novo Compromisso</span>
            </button>
          </div>
        </div>
        <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
          <Calendar commitments={filteredCommitments} darkMode={darkMode} setSelectedCommitment={setSelectedCommitment} onDayClick={setSelectedDay} />
        </div>
        <div
          className={`${
            darkMode ? "bg-gray-800" : "bg-white"
          } p-6 rounded-lg shadow-sm border`}
        >
          {/* Filtros aqui */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredCommitments.map((c) => (
              <CommitmentCard
                key={c.id}
                commitment={c}
                onClick={setSelectedCommitment}
                getStatusColor={getStatusColor}
                darkMode={darkMode}
              />
            ))}
          </div>
          {filteredCommitments.length === 0 && (
            <div className={`text-center py-12 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              <Filter className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Nenhum compromisso encontrado</p>
            </div>
          )}
        </div>
        <NewCommitmentModal
          isOpen={isModalOpen}
          setIsOpen={setIsModalOpen}
          darkMode={darkMode}
          onSave={loadCommitments}
        />
        <DayCommitmentsModal
          isOpen={!!selectedDay}
          setIsOpen={() => setSelectedDay(null)}
          dayData={selectedDay}
          darkMode={darkMode}
          setSelectedCommitment={setSelectedCommitment}
        />
      </div>
    );
};