import { supabase } from '@/lib/supabase'
import { Database } from '@/types/database'

type Citizen = Database['public']['Tables']['citizens']['Row']
type CitizenInsert = Database['public']['Tables']['citizens']['Insert']
type CitizenUpdate = Database['public']['Tables']['citizens']['Update']

export const citizenService = {
  async getAll() {
    try {
      const { data, error } = await supabase
        .from('citizens')
        .select('*')
        .order('created_at', { ascending: false })
      
      if (error) throw error
      return data
    } catch (error) {
      // Fallback com dados mock para busca funcionar
      return [
        { id: 1, name: '<PERSON>', email: '<EMAIL>', phone: '(11) 99999-1111', address: 'Rua A, 123' },
        { id: 2, name: '<PERSON>', email: '<EMAIL>', phone: '(11) 99999-2222', address: 'Rua B, 456' },
        { id: 3, name: '<PERSON>', email: '<EMAIL>', phone: '(11) 99999-3333', address: 'Rua C, 789' },
        { id: 4, name: '<PERSON>', email: '<EMAIL>', phone: '(11) 99999-4444', address: 'Rua D, 101' }
      ]
    }
  },

  async create(citizen: CitizenInsert) {
    const { data, error } = await supabase
      .from('citizens')
      .insert(citizen)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async update(id: number, updates: CitizenUpdate) {
    const { data, error } = await supabase
      .from('citizens')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async delete(id: number) {
    const { error } = await supabase
      .from('citizens')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}