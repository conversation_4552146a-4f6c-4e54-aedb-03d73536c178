import { supabase } from '@/lib/supabase'
import { Database } from '@/types/database'

type Citizen = Database['public']['Tables']['citizens']['Row']
type CitizenInsert = Database['public']['Tables']['citizens']['Insert']
type CitizenUpdate = Database['public']['Tables']['citizens']['Update']

export const citizenService = {
  async getAll() {
    const { data, error } = await supabase
      .from('citizens')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  async create(citizen: CitizenInsert) {
    const { data, error } = await supabase
      .from('citizens')
      .insert(citizen)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async update(id: number, updates: CitizenUpdate) {
    const { data, error } = await supabase
      .from('citizens')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async delete(id: number) {
    const { error } = await supabase
      .from('citizens')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}