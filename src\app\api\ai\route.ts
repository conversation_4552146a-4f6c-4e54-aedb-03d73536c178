import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { rateLimit, getRateLimitHeaders } from '@/lib/rateLimit';
import { aiRequestSchema, validateAndSanitize } from '@/lib/validation';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface Message {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'anonymous';
    const rateLimitResult = rateLimit(ip, 30, 60000); // 30 requests per minute
    
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Try again later.' },
        { 
          status: 429,
          headers: getRateLimitHeaders(30, rateLimitResult.remaining, rateLimitResult.resetTime)
        }
      );
    }

    const authHeader = request.headers.get('authorization');
    const requestData = await request.json();
    
    // Validar dados de entrada
    const validation = validateAndSanitize(aiRequestSchema, requestData);
    if (!validation.success) {
      return NextResponse.json(
        { error: validation.error },
        { status: 400 }
      );
    }
    
    const { page, messages } = validation.data;

    let finalMessages: Message[] = [];

    // Se tem API key, usar sistema multi-tenant
    if (authHeader?.startsWith('Bearer ')) {
      const apiKey = authHeader.replace('Bearer ', '');
      
      const { data: tenantData } = await supabase
        .from('tenant_api_keys')
        .select('tenant_id, tenants(id, name)')
        .eq('api_key', apiKey)
        .eq('is_active', true)
        .single();

      if (tenantData) {
        const tenant = tenantData.tenants;
        
        const [contextResult, promptResult] = await Promise.all([
          supabase.from('contexts').select('content').eq('tenant_id', tenant.id).eq('page', page).single(),
          supabase.from('prompt_templates').select('system_prompt').eq('tenant_id', tenant.id).eq('page', page).single()
        ]);

        finalMessages = [
          {
            role: 'system',
            content: promptResult.data?.system_prompt || 'Você é um assistente especializado em política brasileira.'
          }
        ];

        if (contextResult.data?.content) {
          finalMessages.push({ role: 'system', content: contextResult.data.content });
        }
      }
    }

    // Fallback para prompts padrão
    if (finalMessages.length === 0) {
      const systemPrompts = {
        dashboard: 'Você é um assistente especializado em gestão de gabinetes políticos brasileiros.',
        citizens: 'Você é um assistente para gestão de cidadãos.',
        requests: 'Você é um assistente para demandas dos cidadãos.',
        agenda: 'Você é um assistente para agenda de compromissos.',
        projects: 'Você é um assistente para projetos e iniciativas.'
      };
      
      const systemPrompt = systemPrompts[page as keyof typeof systemPrompts] || systemPrompts.dashboard;
      finalMessages = [{ role: 'system', content: systemPrompt + ' Responda sempre em português brasileiro.' }];
    }

    finalMessages.push(...messages);

    // Verificar se precisa de busca na internet
    const userMessage = messages[messages.length - 1]?.content || '';
    const needsSearch = checkIfNeedsSearch(userMessage);
    
    let searchResults = '';
    if (needsSearch && process.env.TAVILY_API_KEY) {
      try {
        const searchResponse = await fetch('https://api.tavily.com/search', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            api_key: process.env.TAVILY_API_KEY,
            query: userMessage,
            search_depth: 'basic',
            include_answer: true,
            include_raw_content: false,
            max_results: 3
          })
        });

        if (searchResponse.ok) {
          const searchData = await searchResponse.json();
          if (searchData.results && searchData.results.length > 0) {
            searchResults = `\n\nInformações atuais da internet:\n${searchData.results.map((r: any) => 
              `- ${r.title}: ${r.content}`
            ).join('\n')}`;
          }
        }
      } catch (searchError) {
        console.error('Erro na busca Tavily:', searchError);
      }
    }

    // Adicionar resultados da busca ao contexto
    if (searchResults) {
      finalMessages.push({
        role: 'system',
        content: `Use as informações atuais abaixo para complementar sua resposta:${searchResults}`
      });
    }

    // Chamar OpenAI
    try {
      const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini',
          messages: finalMessages,
          max_tokens: 1000,
          temperature: 0.7,
        }),
      });

      if (openaiResponse.ok) {
        const data = await openaiResponse.json();
        const aiResponse = data.choices[0]?.message?.content;
        if (aiResponse) {
          return NextResponse.json({ response: aiResponse });
        }
      }
    } catch (openaiError) {
      console.error('Erro OpenAI:', openaiError);
    }

    // Fallback para Hugging Face
    try {
      const hfResponse = await fetch('https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.HUGGINGFACE_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: messages[messages.length - 1]?.content || 'Olá'
        }),
      });

      if (hfResponse.ok) {
        const hfData = await hfResponse.json();
        return NextResponse.json({ 
          response: hfData.generated_text || 'Desculpe, não consegui processar sua solicitação no momento.' 
        });
      }
    } catch (hfError) {
      console.error('Erro Hugging Face:', hfError);
    }

    // Fallback final
    return NextResponse.json({ 
      response: 'Desculpe, nossos serviços estão temporariamente indisponíveis. Tente novamente em alguns minutos.' 
    });

  } catch (error) {
    console.error('Erro na API:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

// Função para detectar se a pergunta precisa de busca na internet
function checkIfNeedsSearch(message: string): boolean {
  const searchKeywords = [
    'hoje', 'agora', 'atual', 'recente', 'último', 'nova', 'novo',
    'cotação', 'preço', 'valor', 'custo',
    'notícia', 'notícias', 'aconteceu', 'evento',
    'eleição', 'política', 'governo', 'presidente',
    'orçamento', 'lei', 'projeto de lei',
    'população', 'habitantes', 'censo',
    'indicador', 'estatística', 'dados',
    'aprovação', 'pesquisa', 'enquete',
    'clima', 'tempo', 'temperatura',
    'resultado', 'placar', 'jogo'
  ];
  
  const lowerMessage = message.toLowerCase();
  return searchKeywords.some(keyword => lowerMessage.includes(keyword));
}