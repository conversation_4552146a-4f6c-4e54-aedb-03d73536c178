import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

interface Message {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    const { page, messages } = await request.json();

    let finalMessages: Message[] = [];

    // Se tem API key, usar sistema multi-tenant
    if (authHeader?.startsWith('Bearer ')) {
      const apiKey = authHeader.replace('Bearer ', '');
      
      const { data: tenantData } = await supabase
        .from('tenant_api_keys')
        .select('tenant_id, tenants(id, name)')
        .eq('api_key', apiKey)
        .eq('is_active', true)
        .single();

      if (tenantData) {
        const tenant = tenantData.tenants;
        
        const [contextResult, promptResult] = await Promise.all([
          supabase.from('contexts').select('content').eq('tenant_id', tenant.id).eq('page', page).single(),
          supabase.from('prompt_templates').select('system_prompt').eq('tenant_id', tenant.id).eq('page', page).single()
        ]);

        finalMessages = [
          {
            role: 'system',
            content: promptResult.data?.system_prompt || 'Você é um assistente especializado em política brasileira.'
          }
        ];

        if (contextResult.data?.content) {
          finalMessages.push({ role: 'system', content: contextResult.data.content });
        }
      }
    }

    // Fallback para prompts padrão
    if (finalMessages.length === 0) {
      const systemPrompts = {
        dashboard: 'Você é um assistente especializado em gestão de gabinetes políticos brasileiros.',
        citizens: 'Você é um assistente para gestão de cidadãos.',
        requests: 'Você é um assistente para demandas dos cidadãos.',
        agenda: 'Você é um assistente para agenda de compromissos.',
        projects: 'Você é um assistente para projetos e iniciativas.'
      };
      
      const systemPrompt = systemPrompts[page as keyof typeof systemPrompts] || systemPrompts.dashboard;
      finalMessages = [{ role: 'system', content: systemPrompt + ' Responda sempre em português brasileiro.' }];
    }

    finalMessages.push(...messages);

    // Chamar OpenAI
    try {
      const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini',
          messages: finalMessages,
          max_tokens: 1000,
          temperature: 0.7,
        }),
      });

      if (openaiResponse.ok) {
        const data = await openaiResponse.json();
        const aiResponse = data.choices[0]?.message?.content;
        if (aiResponse) {
          return NextResponse.json({ response: aiResponse });
        }
      }
    } catch (openaiError) {
      console.error('Erro OpenAI:', openaiError);
    }

    // Fallback para Hugging Face
    try {
      const hfResponse = await fetch('https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.HUGGINGFACE_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: messages[messages.length - 1]?.content || 'Olá'
        }),
      });

      if (hfResponse.ok) {
        const hfData = await hfResponse.json();
        return NextResponse.json({ 
          response: hfData.generated_text || 'Desculpe, não consegui processar sua solicitação no momento.' 
        });
      }
    } catch (hfError) {
      console.error('Erro Hugging Face:', hfError);
    }

    // Fallback final
    return NextResponse.json({ 
      response: 'Desculpe, nossos serviços estão temporariamente indisponíveis. Tente novamente em alguns minutos.' 
    });

  } catch (error) {
    console.error('Erro na API:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}