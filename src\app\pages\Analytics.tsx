"use client";
import { useState, useEffect } from 'react';
import { StatCard } from './StatCard';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/app/components/ui/tabs";
import { Users, FileText, BarChart, TrendingUp, Calendar, CheckCircle, Clock, Target } from 'lucide-react';
import { citizenService } from '@/services/citizenService';
import { demandService } from '@/services/demandService';
import { commitmentService } from '@/services/commitmentService';
import { projectService } from '@/services/projectService';

const BarChartComponent = ({ data, title, darkMode }: { data: Record<string, number>, title: string, darkMode: boolean }) => (
    <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
        <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{title}</h3>
        <div className="space-y-4">
            {Object.entries(data || {}).map(([key, value]) => (
                <div key={key} className="flex items-center">
                    <span className={`w-32 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{key}</span>
                    <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-6">
                        <div className="bg-blue-600 h-6 rounded-full text-white text-xs flex items-center justify-center" style={{ width: `${(value / Math.max(...Object.values(data || {}))) * 100}%` }}>
                            {value}
                        </div>
                    </div>
                </div>
            ))}
        </div>
    </div>
);

const MetricsCard = ({ title, value, subtitle, icon: Icon, color, darkMode }: { title: string, value: number, subtitle?: string, icon: any, color: string, darkMode: boolean }) => (
    <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
        <div className="flex items-center justify-between">
            <div>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{title}</p>
                <p className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{value}</p>
                {subtitle && <p className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>{subtitle}</p>}
            </div>
            <Icon className={`w-8 h-8 text-${color}-500`} />
        </div>
    </div>
);

export const Analytics = ({ darkMode }: { darkMode: boolean }) => {
    const [analytics, setAnalytics] = useState({
        totalCitizens: 0,
        totalRequests: 0,
        resolvedRequests: 0,
        pendingRequests: 0,
        totalCommitments: 0,
        todayCommitments: 0,
        totalProjects: 0,
        activeProjects: 0,
        requestsByType: {},
        requestsByStatus: {},
        projectsByStatus: {},
        commitmentsByMonth: {}
    });
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        loadAnalyticsData();
    }, []);

    const loadAnalyticsData = async () => {
        try {
            const [citizensData, demandsData, commitmentsData, projectsData] = await Promise.all([
                citizenService.getAll(),
                demandService.getAll(),
                commitmentService.getAll(),
                projectService.getAll()
            ]);

            const today = new Date().toISOString().split('T')[0];
            const resolvedRequests = demandsData?.filter(d => d.status === 'resolvido').length || 0;
            const pendingRequests = demandsData?.filter(d => d.status !== 'resolvido').length || 0;
            const todayCommitments = commitmentsData?.filter(c => c.date === today).length || 0;
            const activeProjects = projectsData?.filter(p => p.status === 'em_andamento').length || 0;
            
            // Group by type
            const requestsByType = demandsData?.reduce((acc, demand) => {
                acc[demand.type] = (acc[demand.type] || 0) + 1;
                return acc;
            }, {}) || {};

            // Group by status
            const requestsByStatus = demandsData?.reduce((acc, demand) => {
                acc[demand.status] = (acc[demand.status] || 0) + 1;
                return acc;
            }, {}) || {};

            const projectsByStatus = projectsData?.reduce((acc, project) => {
                acc[project.status] = (acc[project.status] || 0) + 1;
                return acc;
            }, {}) || {};

            setAnalytics({
                totalCitizens: citizensData?.length || 0,
                totalRequests: demandsData?.length || 0,
                resolvedRequests,
                pendingRequests,
                totalCommitments: commitmentsData?.length || 0,
                todayCommitments,
                totalProjects: projectsData?.length || 0,
                activeProjects,
                requestsByType,
                requestsByStatus,
                projectsByStatus,
                commitmentsByMonth: {}
            });
        } catch (error) {
            console.error('Error loading analytics data:', error);
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            <div className={`text-center py-12 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p>Carregando analytics...</p>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Analytics & Métricas</h1>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        Visualização de dados e métricas de desempenho
                    </p>
                </div>
            </div>
            
            <Tabs defaultValue="overview" className="w-full">
                <TabsList className={`grid w-full grid-cols-4 ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100'}`}>
                    <TabsTrigger value="overview">Visão Geral</TabsTrigger>
                    <TabsTrigger value="demands">Demandas</TabsTrigger>
                    <TabsTrigger value="projects">Projetos</TabsTrigger>
                    <TabsTrigger value="performance">Performance</TabsTrigger>
                </TabsList>
                
                <TabsContent value="overview" className="space-y-6">
                    {/* Métricas Principais */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <MetricsCard 
                            title="Total de Cidadãos" 
                            value={analytics.totalCitizens} 
                            subtitle="Cadastrados no sistema"
                            icon={Users} 
                            color="blue" 
                            darkMode={darkMode} 
                        />
                        <MetricsCard 
                            title="Total de Demandas" 
                            value={analytics.totalRequests} 
                            subtitle={`${analytics.resolvedRequests} resolvidas`}
                            icon={FileText} 
                            color="green" 
                            darkMode={darkMode} 
                        />
                        <MetricsCard 
                            title="Compromissos Hoje" 
                            value={analytics.todayCommitments} 
                            subtitle={`${analytics.totalCommitments} total`}
                            icon={Calendar} 
                            color="purple" 
                            darkMode={darkMode} 
                        />
                        <MetricsCard 
                            title="Projetos Ativos" 
                            value={analytics.activeProjects} 
                            subtitle={`${analytics.totalProjects} total`}
                            icon={Target} 
                            color="orange" 
                            darkMode={darkMode} 
                        />
                    </div>

                    {/* Gráficos de Distribuição */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <BarChartComponent data={analytics.requestsByType} title="Demandas por Tipo" darkMode={darkMode} />
                        <BarChartComponent data={analytics.requestsByStatus} title="Demandas por Status" darkMode={darkMode} />
                    </div>
                </TabsContent>
                
                <TabsContent value="demands" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <StatCard title="Total de Demandas" value={analytics.totalRequests} change={0} icon={FileText} color="blue" darkMode={darkMode} />
                        <StatCard title="Demandas Resolvidas" value={analytics.resolvedRequests} change={0} icon={CheckCircle} color="green" darkMode={darkMode} />
                        <StatCard title="Demandas Pendentes" value={analytics.pendingRequests} change={0} icon={Clock} color="yellow" darkMode={darkMode} />
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <BarChartComponent data={analytics.requestsByType} title="Distribuição por Tipo" darkMode={darkMode} />
                        <BarChartComponent data={analytics.requestsByStatus} title="Distribuição por Status" darkMode={darkMode} />
                    </div>

                    <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                        <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>Taxa de Resolução</h3>
                        <div className="flex items-center justify-between">
                            <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Demandas Resolvidas</span>
                            <span className={`text-2xl font-bold ${analytics.resolvedRequests > analytics.pendingRequests ? 'text-green-500' : 'text-yellow-500'}`}>
                                {analytics.totalRequests > 0 ? Math.round((analytics.resolvedRequests / analytics.totalRequests) * 100) : 0}%
                            </span>
                        </div>
                        <div className="mt-2 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div 
                                className="bg-green-500 h-2 rounded-full" 
                                style={{ width: `${analytics.totalRequests > 0 ? (analytics.resolvedRequests / analytics.totalRequests) * 100 : 0}%` }}
                            ></div>
                        </div>
                    </div>
                </TabsContent>
                
                <TabsContent value="projects" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <StatCard title="Total de Projetos" value={analytics.totalProjects} change={0} icon={Target} color="blue" darkMode={darkMode} />
                        <StatCard title="Projetos Ativos" value={analytics.activeProjects} change={0} icon={TrendingUp} color="green" darkMode={darkMode} />
                    </div>

                    <BarChartComponent data={analytics.projectsByStatus} title="Projetos por Status" darkMode={darkMode} />
                </TabsContent>
                
                <TabsContent value="performance" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border text-center`}>
                            <div className="text-3xl font-bold text-blue-500 mb-2">
                                {analytics.totalRequests > 0 ? Math.round((analytics.resolvedRequests / analytics.totalRequests) * 100) : 0}%
                            </div>
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Taxa de Resolução</p>
                        </div>
                        
                        <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border text-center`}>
                            <div className="text-3xl font-bold text-green-500 mb-2">
                                {analytics.totalProjects > 0 ? Math.round((analytics.activeProjects / analytics.totalProjects) * 100) : 0}%
                            </div>
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Projetos Ativos</p>
                        </div>
                        
                        <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border text-center`}>
                            <div className="text-3xl font-bold text-purple-500 mb-2">
                                {analytics.totalCommitments}
                            </div>
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Compromissos</p>
                        </div>
                        
                        <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border text-center`}>
                            <div className="text-3xl font-bold text-orange-500 mb-2">
                                {analytics.totalCitizens}
                            </div>
                            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Base de Contatos</p>
                        </div>
                    </div>

                    <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                        <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>Resumo de Performance</h3>
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Eficiência na Resolução</span>
                                <div className="flex items-center space-x-2">
                                    <div className={`w-20 ${darkMode ? 'bg-gray-700' : 'bg-gray-200'} rounded-full h-2`}>
                                        <div className="bg-green-600 h-2 rounded-full" style={{ width: `${analytics.totalRequests > 0 ? (analytics.resolvedRequests / analytics.totalRequests) * 100 : 0}%` }}></div>
                                    </div>
                                    <span className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                                        {analytics.totalRequests > 0 ? Math.round((analytics.resolvedRequests / analytics.totalRequests) * 100) : 0}%
                                    </span>
                                </div>
                            </div>
                            
                            <div className="flex items-center justify-between">
                                <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Projetos em Andamento</span>
                                <div className="flex items-center space-x-2">
                                    <div className={`w-20 ${darkMode ? 'bg-gray-700' : 'bg-gray-200'} rounded-full h-2`}>
                                        <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${analytics.totalProjects > 0 ? (analytics.activeProjects / analytics.totalProjects) * 100 : 0}%` }}></div>
                                    </div>
                                    <span className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                                        {analytics.totalProjects > 0 ? Math.round((analytics.activeProjects / analytics.totalProjects) * 100) : 0}%
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    );
};