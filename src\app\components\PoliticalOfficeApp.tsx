"use client";
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Header } from '../pages/Header';
import { Sidebar } from '../pages/Sidebar';
import { Dashboard } from '../pages/Dashboard';
import { Agenda } from '../pages/Agenda';
import { Citizens } from '../pages/Citizens';
import { Requests } from '../pages/Requests';
import { Projects } from '../pages/Projects';
import { Communications } from '../pages/Communications';
import { Analytics } from '../pages/Analytics';
import { Team } from '../pages/Team';
import { Settings } from '../pages/Settings';
import { AIAssistant } from '../pages/AIAssistant';
import { AIHub } from '../pages/AIHub';
import { Reports } from '../pages/Reports';
import { UserProfile } from '../pages/UserProfile';

const PoliticalOfficeApp = () => {
  // --- STATE MANAGEMENT ---
  const [currentView, setCurrentView] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [selectedCitizen, setSelectedCitizen] = useState(null);
  const [selectedCommitment, setSelectedCommitment] = useState(null);
  
  // Dados do usuário vem do contexto de autenticação
  const { user } = useAuth();

  const [darkMode, setDarkMode] = useState(false);

  // --- DADOS SIMULADOS PARA GABINETE POLÍTICO ---

  // Notificações
  const [notifications, setNotifications] = useState([
    { id: 1, type: 'urgent', title: 'Votação Urgente na Câmara', description: 'PL 123/2025 - Orçamento da União', time: 'em 30 minutos', read: false, color: 'red' },
    { id: 2, type: 'warning', title: 'Demanda de Liderança', description: 'João da Saúde solicita reunião sobre hospital regional', time: 'há 1 hora', read: false, color: 'yellow' },
    { id: 3, type: 'reminder', title: 'Entrevista Agendada', description: 'Entrevista para a Rádio CBN', time: 'Amanhã às 09:00', read: false, color: 'blue' },
    { id: 4, type: 'success', title: 'Emenda Aprovada', description: 'Emenda para a educação de jovens foi aprovada', time: 'há 3 horas', read: true, color: 'green' }
  ]);
  const unreadNotifications = notifications.filter(n => !n.read).length;

  // Cidadãos e Contatos
  const [citizens, setCitizens] = useState([
    { id: 1, name: 'Maria Oliveira', email: '<EMAIL>', phone: '(61) 99999-1111', region: 'Asa Sul, Brasília', demandArea: 'Saúde', leader: 'João da Saúde', lastContact: '2025-08-15', status: 'apoiador', notes: 'Solicitou melhorias no posto de saúde local.' },
    { id: 2, name: 'José Pereira', email: '<EMAIL>', phone: '(61) 98888-2222', region: 'Taguatinga, Brasília', demandArea: 'Educação', leader: 'Ana dos Professores', lastContact: '2025-08-10', status: 'neutro', notes: 'Preocupado com a falta de vagas em creches.' },
    { id: 3, name: 'Antônio Costa', email: '<EMAIL>', phone: '(61) 97777-3333', region: 'Guará, Brasília', demandArea: 'Segurança', leader: 'Sargento Lima', lastContact: '2025-08-20', status: 'apoiador', notes: 'Líder comunitário, pede mais policiamento no bairro.' }
  ]);

  // Agenda de Compromissos
  const [commitments, setCommitments] = useState([
    { id: 1, title: 'Reunião com Sindicato dos Professores', responsible: 'Carlos Andrade', date: '2025-09-02', time: '10:00', duration: 60, type: 'Reunião', location: 'Gabinete', status: 'agendado', topic: 'Piso salarial e plano de carreira.' },
    { id: 2, title: 'Sessão Plenária - Votação Orçamento', responsible: 'Dep. Joana Silva', date: '2025-09-02', time: '14:00', duration: 180, type: 'Evento Oficial', location: 'Plenário da Câmara', status: 'confirmado', topic: 'Votação do Orçamento da União para 2026.' },
    { id: 3, title: 'Visita à Comunidade do Sol Nascente', responsible: 'Mariana Lima', date: '2025-09-03', time: '09:30', duration: 120, type: 'Visita', location: 'Sol Nascente, Ceilândia', status: 'realizado', topic: 'Ouvir demandas da população sobre saneamento básico.' }
  ]);

  // Demandas e Solicitações dos Cidadãos
  const [requests, setRequests] = useState([
    { id: 1, citizenName: 'Maria Oliveira', date: '2025-08-15', type: 'Ofício', responsibleStaff: 'Mariana Lima', subject: 'Reforma do Posto de Saúde', description: 'Solicita envio de ofício para a Secretaria de Saúde pedindo reforma.', status: 'em_andamento', priority: 'alta' },
    { id: 2, citizenName: 'José Pereira', date: '2025-08-10', type: 'Indicação', responsibleStaff: 'Carlos Andrade', subject: 'Construção de Creche', description: 'Indicação legislativa para construção de nova creche em Taguatinga.', status: 'resolvido', priority: 'media' },
    { id: 3, citizenName: 'Antônio Costa', date: '2025-08-20', type: 'Requerimento', responsibleStaff: 'Sofia Ribeiro', subject: 'Dados de Segurança Pública', description: 'Requerimento de informações sobre o efetivo policial no Guará.', status: 'protocolado', priority: 'baixa' }
  ]);

  // Projetos e Iniciativas (ex: Projetos de Lei)
  const [projects, setProjects] = useState([
    { id: 1, name: 'PL 245/2025 - Incentivo ao Primeiro Emprego', protocol: 'PL-245/2025', category: 'Projeto de Lei', status: 'em_tramitacao', budget: 0, description: 'Cria incentivos fiscais para empresas que contratarem jovens.' },
    { id: 2, name: 'Emenda 42 - Orçamento da Saúde', protocol: 'EMD-42/2025', category: 'Emenda', status: 'aprovado', budget: 5000000, description: 'Destina R$ 5 milhões para hospitais regionais.' },
    { id: 3, name: 'Indicação 112 - Ciclovia na EPTG', protocol: 'IND-112/2025', category: 'Indicação', status: 'enviado', budget: 0, description: 'Sugere ao poder executivo a construção de ciclovia na EPTG.' }
  ]);

  // Dados para Dashboard
  const [analytics, setAnalytics] = useState({
    totalCitizens: 1250,
    newRequests: 45,
    resolvedRequests: 18,
    commitmentsToday: 2,
    commitmentsWeek: 15,
    socialMediaEngagement: 12.5, // em %
    positiveMentions: 85, // em %
    monthlyGrowth: {
      citizens: 5,
      requests: 15,
      engagement: 2.1
    },
    requestsByArea: {
      'Saúde': 45,
      'Educação': 32,
      'Segurança': 28,
      'Infraestrutura': 25,
      'Social': 20
    }
  });

  const [socialPosts, setSocialPosts] = useState([
    { id: 1, platform: 'Facebook', content: 'Hoje visitamos a comunidade do Sol Nascente e ouvimos as demandas dos moradores. #Trabalho #Comunidade', date: '2025-09-03', likes: 1500, comments: 250, shares: 120 },
    { id: 2, platform: 'Instagram', content: 'Aprovada nossa emenda para a saúde! Mais R$ 5 milhões para os hospitais regionais. ✅ #Saúde #Conquista', date: '2025-09-01', likes: 3200, comments: 450, shares: 0 },
    { id: 3, platform: 'Twitter', content: 'Na pauta de hoje, a votação do orçamento da União. Defenderemos os interesses da nossa gente. 🇧🇷', date: '2025-09-02', likes: 800, comments: 150, shares: 50 },
  ]);

  const [officeTeam, setOfficeTeam] = useState([
    { id: 1, name: 'Mariana Lima', role: 'Assessora de Articulação Política', email: '<EMAIL>', phone: '(61) 99999-0001', area: 'Articulação Política', status: 'ativo', tasksToday: 5, tasksWeek: 25, rating: 4.9 },
    { id: 2, name: 'Pedro Alves', role: 'Assessor de Imprensa', email: '<EMAIL>', phone: '(61) 99999-0002', area: 'Comunicação', status: 'ativo', tasksToday: 8, tasksWeek: 35, rating: 4.8 },
    { id: 3, name: 'Sofia Ribeiro', role: 'Assessora Jurídica', email: '<EMAIL>', phone: '(61) 99999-0003', area: 'Jurídico', status: 'ativo', tasksToday: 3, tasksWeek: 15, rating: 4.9 }
  ]);

  // --- EFEITOS E FUNÇÕES AUXILIARES ---

  const markNotificationAsRead = (id) => setNotifications(prev => prev.map(n => n.id === id ? { ...n, read: true } : n));
  const markAllAsRead = () => setNotifications(prev => prev.map(n => ({ ...n, read: true })));

  // Funções de Estilo
  const getPriorityColor = (priority: 'alta' | 'media' | 'baixa') => {
    const styles = {
      alta: darkMode ? 'text-red-400 bg-red-900/30' : 'text-red-600 bg-red-50',
      media: darkMode ? 'text-yellow-400 bg-yellow-900/30' : 'text-yellow-600 bg-yellow-50',
      baixa: darkMode ? 'text-green-400 bg-green-900/30' : 'text-green-600 bg-green-50',
    };
    return styles[priority] || (darkMode ? 'text-gray-400 bg-gray-700' : 'text-gray-600 bg-gray-50');
  };

  const getStatusColor = (status) => {
    const styles = {
        agendado: darkMode ? 'text-blue-400 bg-blue-900/30' : 'text-blue-600 bg-blue-50',
        confirmado: darkMode ? 'text-green-400 bg-green-900/30' : 'text-green-600 bg-green-50',
        realizado: darkMode ? 'text-gray-400 bg-gray-700' : 'text-gray-600 bg-gray-50',
        cancelado: darkMode ? 'text-red-400 bg-red-900/30' : 'text-red-600 bg-red-50',
        em_andamento: darkMode ? 'text-orange-400 bg-orange-900/30' : 'text-orange-600 bg-orange-50',
        resolvido: darkMode ? 'text-green-400 bg-green-900/30' : 'text-green-600 bg-green-50',
        protocolado: darkMode ? 'text-purple-400 bg-purple-900/30' : 'text-purple-600 bg-purple-50',
        em_tramitacao: darkMode ? 'text-yellow-400 bg-yellow-900/30' : 'text-yellow-600 bg-yellow-50',
        aprovado: darkMode ? 'text-green-400 bg-green-900/30' : 'text-green-600 bg-green-50',
        enviado: darkMode ? 'text-blue-400 bg-blue-900/30' : 'text-blue-600 bg-blue-50',
    };
    return styles[status] || (darkMode ? 'text-gray-400 bg-gray-700' : 'text-gray-600 bg-gray-50');
};

  // Seletor de conteúdo principal
  const renderContent = () => {
    switch(currentView) {
      case 'dashboard': return <Dashboard getStatusColor={getStatusColor} darkMode={darkMode} />;
      case 'agenda': return <Agenda setSelectedCommitment={setSelectedCommitment} getStatusColor={getStatusColor} darkMode={darkMode} />;
      case 'citizens': return <Citizens setSelectedCitizen={setSelectedCitizen} darkMode={darkMode} />;
      case 'requests': return <Requests getPriorityColor={getPriorityColor} getStatusColor={getStatusColor} darkMode={darkMode} />;
      case 'projects': return <Projects getStatusColor={getStatusColor} darkMode={darkMode} />;
      case 'communications': return <Communications darkMode={darkMode} />;
      case 'analytics': return <Analytics darkMode={darkMode} />;
      case 'ai': return <AIHub darkMode={darkMode} />;
      case 'team': return <Team darkMode={darkMode} />;
      case 'reports': return <Reports darkMode={darkMode} />;
      case 'profile': return <UserProfile darkMode={darkMode} />;
      case 'settings': return <Settings user={user} darkMode={darkMode} setDarkMode={setDarkMode} />;
      default: return <Dashboard getStatusColor={getStatusColor} darkMode={darkMode} />;
    }
  };

  // --- JSX PRINCIPAL DO COMPONENTE ---
  return (
    <div className={`min-h-screen transition-colors ${darkMode ? 'bg-gray-900' : 'bg-gray-100'}`}>
      <Header 
        user={user}
        darkMode={darkMode}
        setDarkMode={setDarkMode}
        setSidebarOpen={setSidebarOpen}
        notifications={notifications}
        unreadNotifications={unreadNotifications}
        markNotificationAsRead={markNotificationAsRead}
        markAllAsRead={markAllAsRead}
        onProfileClick={() => setCurrentView('profile')}
        onSettingsClick={() => setCurrentView('settings')}
      />

      <div className="flex">
        <Sidebar 
          currentView={currentView}
          setCurrentView={setCurrentView}
          setSidebarOpen={setSidebarOpen}
          darkMode={darkMode}
          sidebarOpen={sidebarOpen}
        />

        {/* Conteúdo Principal */}
        <main className="flex-1 md:ml-0">
          <div className="p-6 max-w-7xl mx-auto">
            {renderContent()}
          </div>
        </main>
      </div>

      {sidebarOpen && <div className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden" onClick={() => setSidebarOpen(false)} />}
      
      {/* Assistente de IA */}
      <AIAssistant 
        darkMode={darkMode} 
        context={currentView as any}
        data={{ analytics, citizens, requests, commitments }}
      />
    </div>
  );
};

export default PoliticalOfficeApp;
