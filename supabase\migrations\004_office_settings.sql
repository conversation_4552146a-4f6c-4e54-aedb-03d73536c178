-- Create office_settings table
CREATE TABLE office_settings (
  id BIGSERIAL PRIMARY KEY,
  office_name TEXT NOT NULL DEFAULT 'Gabinete',
  office_type TEXT NOT NULL DEFAULT 'vereador', -- vereador, prefeito, deputado, etc
  holder_name TEXT NOT NULL,
  institution_name TEXT NOT NULL, -- ex: Câmara Municipal de São Paulo
  city TEXT NOT NULL,
  state TEXT NOT NULL,
  address TEXT,
  phone TEXT,
  email TEXT,
  website TEXT,
  logo_url TEXT,
  coat_of_arms_url TEXT,
  social_media JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create users table for authentication and roles
CREATE TABLE users (
  id BIGSERIAL PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'assessor', -- titular, assessor_principal, assessor_marketing, assessor
  phone TEXT,
  avatar_url TEXT,
  permissions JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create updated_at triggers
CREATE TRIGGER update_office_settings_updated_at BEFORE UPDATE ON office_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE office_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (adjust as needed for production)
CREATE POLICY "Allow all operations on office_settings" ON office_settings FOR ALL USING (true);
CREATE POLICY "Allow all operations on users" ON users FOR ALL USING (true);

-- Nota: Os dados padrão serão criados quando o primeiro usuário se cadastrar