"use client";
import { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface Commitment {
  id: number;
  title: string;
  date: string;
  time: string;
  status: string;
}

interface CalendarProps {
  commitments: Commitment[];
  darkMode: boolean;
  setSelectedCommitment: (commitment: Commitment) => void;
  onDayClick: (dayData: {date: string, commitments: Commitment[]}) => void;
}

export const Calendar = ({ commitments, darkMode, setSelectedCommitment, onDayClick }: CalendarProps) => {
  const [currentDate, setCurrentDate] = useState(new Date());

  const monthNames = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', '<PERSON><PERSON>', 'Jun<PERSON>',
    'Jul<PERSON>', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ];

  const dayNames = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day);
    }
    
    return days;
  };

  const getCommitmentsForDay = (day: number) => {
    if (!day) return [];
    const dateStr = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    return commitments.filter(c => c.date === dateStr);
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const days = getDaysInMonth(currentDate);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className={`text-xl font-semibold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
          {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
        </h2>
        <div className="flex space-x-2">
          <button
            onClick={() => navigateMonth('prev')}
            className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-600'}`}
          >
            <ChevronLeft className="w-5 h-5" />
          </button>
          <button
            onClick={() => navigateMonth('next')}
            className={`p-2 rounded-lg ${darkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-600'}`}
          >
            <ChevronRight className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-7 gap-1">
        {dayNames.map(day => (
          <div key={day} className={`p-2 text-center text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            {day}
          </div>
        ))}
        
        {days.map((day, index) => {
          const dayCommitments = day ? getCommitmentsForDay(day) : [];
          const isToday = day && 
            new Date().getDate() === day && 
            new Date().getMonth() === currentDate.getMonth() && 
            new Date().getFullYear() === currentDate.getFullYear();

          return (
            <div
              key={index}
              className={`min-h-[80px] p-1 border rounded-lg ${
                darkMode ? 'border-gray-700' : 'border-gray-200'
              } ${day ? 'cursor-pointer hover:bg-opacity-50' : ''} ${
                isToday ? (darkMode ? 'bg-blue-900 bg-opacity-30' : 'bg-blue-100') : ''
              } ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'}`}
              onClick={() => day && dayCommitments.length > 0 && onDayClick({
                date: `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`,
                commitments: dayCommitments
              })}
            >
              {day && (
                <>
                  <div className={`text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'} ${isToday ? 'font-bold' : ''}`}>
                    {day}
                  </div>
                  <div className="space-y-1">
                    {dayCommitments.slice(0, 2).map(commitment => (
                      <div
                        key={commitment.id}
                        onClick={() => setSelectedCommitment(commitment)}
                        className={`text-xs p-1 rounded cursor-pointer truncate ${
                          commitment.status === 'confirmado' 
                            ? 'bg-green-100 text-green-800' 
                            : commitment.status === 'pendente'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {commitment.time} - {commitment.title}
                      </div>
                    ))}
                    {dayCommitments.length > 2 && (
                      <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        +{dayCommitments.length - 2} mais
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};