"use client";
import { useState } from 'react';
import React from 'react';
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Bot, Send, Minimize2, Maximize2, X, Lightbulb, TrendingUp, Users, Calendar } from 'lucide-react';
import { aiService } from '@/services/aiService';

interface AIAssistantProps {
  darkMode: boolean;
  context?: 'dashboard' | 'citizens' | 'requests' | 'agenda' | 'analytics';
  data?: any;
}

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

export const AIAssistant = ({ darkMode, context = 'dashboard', data }: AIAssistantProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Olá! Sou seu assistente de IA. Como posso ajudar você hoje?',
      timestamp: new Date(),
      suggestions: ['Analisar demandas', 'Sugerir agenda', 'Insights de cidadãos', 'Relatório rápido']
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  // Integração com IA real
  const getAIResponse = async (userMessage: string): Promise<Message> => {
    try {
      const response = await aiService.generateResponse(userMessage, {
        page: context,
        data: data,
        userRole: 'political_office'
      });

      return {
        id: Date.now().toString(),
        type: 'ai',
        content: response,
        timestamp: new Date(),
        suggestions: getSuggestions(context)
      };
    } catch (error) {
      return {
        id: Date.now().toString(),
        type: 'ai',
        content: 'Desculpe, estou com dificuldades técnicas no momento. Tente novamente em alguns instantes.',
        timestamp: new Date(),
        suggestions: getSuggestions(context)
      };
    }
  };

  // Fallback para respostas offline
  const getOfflineResponse = (userMessage: string): Message => {
    // Resposta genérica quando não há dados reais
    return {
      id: Date.now().toString(),
      type: 'ai',
      content: 'Desculpe, não tenho dados suficientes para fornecer uma análise específica. Conforme você usar o sistema e adicionar dados reais, poderei oferecer insights mais precisos.',
      timestamp: new Date(),
      suggestions: getSuggestions(context)
    };
  };

  const getSuggestions = (currentContext: string): string[] => {
    // Sugestões básicas - serão expandidas conforme dados reais
    return ['Como posso ajudar?', 'Precisa de análises?', 'Quer sugestões?'];
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Chamar IA real
    try {
      const aiResponse = await getAIResponse(inputValue);
      setMessages(prev => [...prev, aiResponse]);
    } catch (error) {
      const fallbackResponse = getOfflineResponse(inputValue);
      setMessages(prev => [...prev, fallbackResponse]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging) return;
    
    const newX = e.clientX - dragStart.x;
    const newY = e.clientY - dragStart.y;
    
    // Limitar dentro da tela
    const maxX = window.innerWidth - 384; // 384px = w-96
    const maxY = window.innerHeight - (isMinimized ? 56 : 400);
    
    setPosition({
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Event listeners para drag
  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragStart]);

  // Posicionar no centro-direita quando abrir
  React.useEffect(() => {
    if (isOpen && position.x === 0 && position.y === 0) {
      const centerX = window.innerWidth * 0.6; // 60% da largura (centro-direita)
      const centerY = (window.innerHeight - 400) / 2; // Centro vertical
      setPosition({ x: centerX, y: centerY });
    }
  }, [isOpen]);

  if (!isOpen) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={() => setIsOpen(true)}
          className="w-14 h-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg"
        >
          <Bot className="w-6 h-6 text-white" />
        </Button>
      </div>
    );
  }

  return (
    <div 
      className="fixed z-50 select-none" 
      style={{ 
        left: `${position.x}px`, 
        top: `${position.y}px`,
        cursor: isDragging ? 'grabbing' : 'default'
      }}
    >
      <Card className="w-96 bg-white border-gray-200 shadow-xl" style={{ height: isMinimized ? '56px' : '400px' }}>
        <CardHeader 
          className="flex flex-row items-center justify-between p-4 pb-2 cursor-grab active:cursor-grabbing"
          onMouseDown={handleMouseDown}
        >
          <div className="flex items-center space-x-2">
            <Bot className="w-5 h-5 text-blue-600" />
            <CardTitle className="text-sm text-gray-900">
              Assistente IA - {context.charAt(0).toUpperCase() + context.slice(1)}
            </CardTitle>
          </div>
          <div className="flex items-center space-x-1" onMouseDown={(e) => e.stopPropagation()}>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setIsMinimized(!isMinimized);
              }}
              className="w-6 h-6 p-0"
            >
              {isMinimized ? <Maximize2 className="w-3 h-3" /> : <Minimize2 className="w-3 h-3" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setIsOpen(false);
              }}
              className="w-6 h-6 p-0"
            >
              <X className="w-3 h-3" />
            </Button>
          </div>
        </CardHeader>

        {!isMinimized && (
          <CardContent className="p-0 flex flex-col" style={{ height: '344px' }}>
            {/* Messages */}
            <div className="flex-1 overflow-y-auto space-y-3 p-4 pb-2">
              {messages.map((message) => (
                <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-[80%] p-3 rounded-lg text-sm ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}>
                    {message.content}
                    {message.suggestions && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {message.suggestions.map((suggestion, index) => (
                          <button
                            key={index}
                            onClick={() => handleSuggestionClick(suggestion)}
                            className="px-2 py-1 text-xs rounded bg-gray-200 hover:bg-gray-300 text-gray-700"
                          >
                            {suggestion}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
              {isTyping && (
                <div className="flex justify-start">
                  <div className="p-3 rounded-lg text-sm bg-gray-100 text-gray-900">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Input */}
            <div className="border-t border-gray-200 bg-gray-50 p-4">
              <div className="flex space-x-2">
                <Input
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="Digite sua pergunta..."
                  className="flex-1 bg-white border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                />
                <Button 
                  onClick={handleSendMessage} 
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-3"
                >
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
};