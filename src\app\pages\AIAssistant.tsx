"use client";
import { useState } from 'react';
import { But<PERSON> } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Bot, Send, Minimize2, Maximize2, X, Lightbulb, TrendingUp, Users, Calendar } from 'lucide-react';
import { aiService } from '@/services/aiService';

interface AIAssistantProps {
  darkMode: boolean;
  context?: 'dashboard' | 'citizens' | 'requests' | 'agenda' | 'analytics';
  data?: any;
}

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

export const AIAssistant = ({ darkMode, context = 'dashboard', data }: AIAssistantProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Olá! Sou seu assistente de IA. Como posso ajudar você hoje?',
      timestamp: new Date(),
      suggestions: ['Analisar demandas', 'Sugerir agenda', 'Insights de cidadãos', 'Relatório rápido']
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  // Integração com IA real
  const getAIResponse = async (userMessage: string): Promise<Message> => {
    try {
      const response = await aiService.generateResponse(userMessage, {
        page: context,
        data: data,
        userRole: 'political_office'
      });

      return {
        id: Date.now().toString(),
        type: 'ai',
        content: response,
        timestamp: new Date(),
        suggestions: getSuggestions(context)
      };
    } catch (error) {
      return {
        id: Date.now().toString(),
        type: 'ai',
        content: 'Desculpe, estou com dificuldades técnicas no momento. Tente novamente em alguns instantes.',
        timestamp: new Date(),
        suggestions: getSuggestions(context)
      };
    }
  };

  // Fallback para respostas offline
  const getOfflineResponse = (userMessage: string): Message => {
    const contextResponses: Record<string, Record<string, string>> = {
      dashboard: {
        'analisar demandas': 'Baseado nos dados atuais, você tem 45 novas demandas este mês, com crescimento de 15%. A área de Saúde lidera com 45 solicitações. Recomendo priorizar as demandas de alta urgência.',
        'sugerir agenda': 'Sugiro reagendar a reunião das 14h para 15h30, pois há sobreposição com a sessão plenária. Também recomendo incluir 30min de buffer entre reuniões.',
        'insights de cidadãos': 'Identifiquei que 68% dos seus contatos são apoiadores ativos. Maria Oliveira e Antônio Costa são influenciadores locais - considere envolvê-los em campanhas.',
        'relatório rápido': 'Resumo: 2 compromissos hoje, 85% menções positivas, engajamento social em 12.5%. Tudo dentro do esperado!'
      },
      citizens: {
        'analisar perfil': 'Este cidadão tem histórico de 3 contatos, sempre sobre saúde. Alta probabilidade de ser apoiador (87%). Recomendo acompanhamento personalizado.',
        'segmentar contatos': 'Identifiquei 3 grupos principais: Apoiadores Ativos (40%), Neutros Engajados (35%), e Novos Contatos (25%). Cada grupo precisa de abordagem diferente.'
      },
      requests: {
        'priorizar demandas': 'Baseado na urgência e impacto, recomendo esta ordem: 1) Reforma Posto de Saúde (alta urgência), 2) Dados Segurança (média), 3) Construção Creche (baixa).',
        'automatizar resposta': 'Posso gerar uma resposta padrão para demandas similares. Isso economizaria 2-3 horas semanais da equipe.'
      },
      agenda: {
        'otimizar horários': 'Análise da agenda mostra possibilidade de otimização. Recomendo reagrupar reuniões por localização.',
        'detectar conflitos': 'Identifiquei 2 conflitos potenciais na agenda da próxima semana.'
      },
      analytics: {
        'insights avançados': 'Baseado nos dados, identifiquei 3 tendências importantes para análise.',
        'predições': 'Modelo preditivo indica aumento de 20% nas demandas de saúde no próximo mês.'
      }
    };

    const responses = contextResponses[context] || contextResponses.dashboard;
    const lowerMessage = userMessage.toLowerCase();
    
    for (const [key, response] of Object.entries(responses)) {
      if (lowerMessage.includes(key.toLowerCase())) {
        return {
          id: Date.now().toString(),
          type: 'ai',
          content: response,
          timestamp: new Date(),
          suggestions: getSuggestions(context)
        };
      }
    }

    // Resposta padrão
    return {
      id: Date.now().toString(),
      type: 'ai',
      content: `Entendi sua pergunta sobre "${userMessage}". Com base no contexto atual, posso ajudar com análises, sugestões de otimização e insights dos dados. O que gostaria de saber especificamente?`,
      timestamp: new Date(),
      suggestions: getSuggestions(context)
    };
  };

  const getSuggestions = (currentContext: string): string[] => {
    const suggestions: Record<string, string[]> = {
      dashboard: ['Analisar tendências', 'Otimizar agenda', 'Relatório semanal', 'Alertas importantes'],
      citizens: ['Segmentar contatos', 'Analisar engajamento', 'Identificar influenciadores', 'Histórico de interações'],
      requests: ['Priorizar demandas', 'Automatizar respostas', 'Análise de urgência', 'Distribuir tarefas'],
      agenda: ['Otimizar horários', 'Detectar conflitos', 'Sugerir preparação', 'Análise de produtividade'],
      analytics: ['Insights avançados', 'Predições', 'Comparar períodos', 'Identificar padrões']
    };
    return suggestions[currentContext] || suggestions.dashboard;
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Chamar IA real
    try {
      const aiResponse = await getAIResponse(inputValue);
      setMessages(prev => [...prev, aiResponse]);
    } catch (error) {
      const fallbackResponse = getOfflineResponse(inputValue);
      setMessages(prev => [...prev, fallbackResponse]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
  };

  if (!isOpen) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={() => setIsOpen(true)}
          className="w-14 h-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg"
        >
          <Bot className="w-6 h-6 text-white" />
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Card className={`w-96 h-96 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} shadow-xl ${isMinimized ? 'h-14' : ''}`}>
        <CardHeader className="flex flex-row items-center justify-between p-4 pb-2">
          <div className="flex items-center space-x-2">
            <Bot className={`w-5 h-5 ${darkMode ? 'text-blue-400' : 'text-blue-600'}`} />
            <CardTitle className={`text-sm ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
              Assistente IA - {context.charAt(0).toUpperCase() + context.slice(1)}
            </CardTitle>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(!isMinimized)}
              className="w-6 h-6 p-0"
            >
              {isMinimized ? <Maximize2 className="w-3 h-3" /> : <Minimize2 className="w-3 h-3" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(false)}
              className="w-6 h-6 p-0"
            >
              <X className="w-3 h-3" />
            </Button>
          </div>
        </CardHeader>

        {!isMinimized && (
          <CardContent className="p-4 pt-0 flex flex-col h-80">
            {/* Messages */}
            <div className="flex-1 overflow-y-auto space-y-3 mb-4">
              {messages.map((message) => (
                <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-[80%] p-3 rounded-lg text-sm ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : darkMode 
                        ? 'bg-gray-700 text-gray-200' 
                        : 'bg-gray-100 text-gray-900'
                  }`}>
                    {message.content}
                    {message.suggestions && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {message.suggestions.map((suggestion, index) => (
                          <button
                            key={index}
                            onClick={() => handleSuggestionClick(suggestion)}
                            className={`px-2 py-1 text-xs rounded ${
                              darkMode 
                                ? 'bg-gray-600 hover:bg-gray-500 text-gray-300' 
                                : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                            }`}
                          >
                            {suggestion}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ))}
              {isTyping && (
                <div className="flex justify-start">
                  <div className={`p-3 rounded-lg text-sm ${darkMode ? 'bg-gray-700 text-gray-200' : 'bg-gray-100 text-gray-900'}`}>
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Input */}
            <div className="flex space-x-2">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder="Digite sua pergunta..."
                className={`flex-1 ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : ''}`}
              />
              <Button onClick={handleSendMessage} size="sm">
                <Send className="w-4 h-4" />
              </Button>
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
};