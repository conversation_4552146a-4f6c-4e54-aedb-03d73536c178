"use client";
import { Calendar as CalendarI<PERSON>, Clock, UserCheck } from 'lucide-react';

export const CommitmentCard = ({ commitment, onClick, getStatusColor, darkMode }: { commitment: any, onClick: (commitment: any) => void, getStatusColor: (status: string) => string, darkMode: boolean }) => (
    <div 
      className={`${darkMode ? 'bg-gray-800 border-gray-700 hover:bg-gray-750' : 'bg-white border-gray-200 hover:shadow-md'} p-4 rounded-lg shadow-sm border transition-all cursor-pointer`}
      onClick={() => onClick(commitment)}
    >
      <div className="flex items-start justify-between mb-3">
        <div>
            <h3 className={`font-medium ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{commitment.title}</h3>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{commitment.type} - {commitment.location}</p>
        </div>
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(commitment.status)}`}>
          {commitment.status}
        </span>
      </div>
      <div className={`space-y-1 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
        <div className="flex items-center space-x-2"><CalendarIcon className="w-4 h-4" /><span>{commitment.date} às {commitment.time}</span></div>
        <div className="flex items-center space-x-2"><Clock className="w-4 h-4" /><span>{commitment.duration} minutos</span></div>
        <div className="flex items-center space-x-2"><UserCheck className="w-4 h-4" /><span>Responsável: {commitment.responsible}</span></div>
      </div>
      {commitment.topic && (
        <div className={`mt-3 p-2 rounded ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
          <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}><strong>Pauta:</strong> {commitment.topic}</p>
        </div>
      )}
    </div>
  );
