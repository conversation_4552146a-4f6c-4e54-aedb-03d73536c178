"use client";
import { But<PERSON> } from "@/app/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";

interface NewCommitmentModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  darkMode: boolean;
  onSave?: () => void;
}

export const NewCommitmentModal = ({ isOpen, setIsOpen, darkMode, onSave }: NewCommitmentModalProps) => {
    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className={`${darkMode ? 'bg-gray-800 border-gray-700' : ''}`}>
                <DialogHeader>
                    <DialogTitle className={`${darkMode ? 'text-white' : ''}`}>Novo Compromisso</DialogTitle>
                    <DialogDescription className={`${darkMode ? 'text-gray-400' : ''}`}>
                        Preencha os detalhes do novo compromisso.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="title" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                            Título
                        </Label>
                        <Input id="title" className={`col-span-3 ${darkMode ? 'text-gray-100' : ''}`} />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="responsible" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                            Responsável
                        </Label>
                        <Input id="responsible" className={`col-span-3 ${darkMode ? 'text-gray-100' : ''}`} />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="date" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                            Data
                        </Label>
                        <Input id="date" type="date" className={`col-span-3 ${darkMode ? 'text-gray-100' : ''}`} />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="time" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                            Hora
                        </Label>
                        <Input id="time" type="time" className={`col-span-3 ${darkMode ? 'text-gray-100' : ''}`} />
                    </div>
                </div>
                <DialogFooter>
                    <Button type="submit" onClick={() => setIsOpen(false)}>Salvar</Button>
                    <Button variant="outline" onClick={() => setIsOpen(false)}>Cancelar</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};
