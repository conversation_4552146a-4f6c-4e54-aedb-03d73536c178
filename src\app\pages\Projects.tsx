"use client";
import { useState, useEffect } from 'react';
import { Plus, MoreHorizontal, Eye, Edit, Trash2 } from 'lucide-react';
import { projectService } from '@/services/projectService';
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/app/components/ui/table";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu"
import { NewProjectModal } from './NewProjectModal';

interface Project {
  id: number;
  name: string;
  protocol: string;
  category: string;
  status: string;
  budget: number;
}

export const Projects = ({ getStatusColor, darkMode }: { getStatusColor: (status: string) => string, darkMode: boolean }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [projects, setProjects] = useState<Project[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        loadProjects();
    }, []);

    const loadProjects = async () => {
        try {
            const data = await projectService.getAll();
            setProjects(data || []);
        } catch (error) {
            console.error('Error loading projects:', error);
        } finally {
            setLoading(false);
        }
    };

    const formatCurrency = (value) => {
        return new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(value);
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <h1 className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Projetos e Iniciativas</h1>
                <button onClick={() => setIsModalOpen(true)} className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
                    <Plus className="w-4 h-4" />
                    <span>Novo Projeto</span>
                </button>
            </div>
            <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                <Table>
                    <TableHeader>
                        <TableRow className={`${darkMode ? 'border-gray-700' : ''}`}>
                            <TableHead className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Nome</TableHead>
                            <TableHead className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Protocolo</TableHead>
                            <TableHead className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Categoria</TableHead>
                            <TableHead className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Status</TableHead>
                            <TableHead className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Orçamento</TableHead>
                            <TableHead className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Ações</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {projects.map((project) => (
                            <TableRow key={project.id} className={`${darkMode ? 'border-gray-700' : ''}`}>
                                <TableCell className={`font-medium ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{project.name}</TableCell>
                                <TableCell className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{project.protocol}</TableCell>
                                <TableCell className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{project.category}</TableCell>
                                <TableCell>
                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                                        {project.status.replace('_', ' ')}
                                    </span>
                                </TableCell>
                                <TableCell className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{formatCurrency(project.budget)}</TableCell>
                                <TableCell>
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <button className={`p-2 rounded-full ${darkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-200 text-gray-600'}`}>
                                                <MoreHorizontal className="w-4 h-4" />
                                            </button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent className={`${darkMode ? 'bg-gray-900 border-gray-700' : 'bg-white'}`}>
                                            <DropdownMenuItem className={`${darkMode ? 'text-gray-300 hover:!bg-gray-700' : 'text-gray-700'}`}><Eye className="w-4 h-4 mr-2" />Ver Detalhes</DropdownMenuItem>
                                            <DropdownMenuItem className={`${darkMode ? 'text-gray-300 hover:!bg-gray-700' : 'text-gray-700'}`}><Edit className="w-4 h-4 mr-2" />Editar</DropdownMenuItem>
                                            <DropdownMenuItem className={`${darkMode ? 'text-red-400 hover:!bg-gray-700' : 'text-red-600'}`}><Trash2 className="w-4 h-4 mr-2" />Excluir</DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
            <NewProjectModal isOpen={isModalOpen} setIsOpen={setIsModalOpen} darkMode={darkMode} onSave={loadProjects} />
        </div>
    )
};