-- Create commitments table (agenda)
CREATE TABLE commitments (
  id BIGSERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  date DATE NOT NULL,
  time TIME NOT NULL,
  status TEXT NOT NULL DEFAULT 'pendente' CHECK (status IN ('confirmado', 'pendente', 'cancelado')),
  location TEXT,
  attendees TEXT[] DEFAULT '{}',
  project_id BIGINT REFERENCES projects(id),
  project_name TEXT,
  demand_id BIGINT REFERENCES demands(id),
  demand_title TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add trigger
CREATE TRIGGER update_commitments_updated_at BEFORE UPDATE ON commitments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS
ALTER TABLE commitments ENABLE ROW LEVEL SECURITY;

-- Create policy
CREATE POLICY "Allow all operations on commitments" ON commitments FOR ALL USING (true);