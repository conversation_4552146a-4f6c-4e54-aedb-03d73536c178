"use client";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { TrendingUp, Calendar, Users, AlertCircle, CheckCircle } from 'lucide-react';

interface PredictiveAnalyticsProps {
  darkMode: boolean;
  analytics: any;
  requests: any[];
  citizens: any[];
}

interface Prediction {
  id: string;
  type: 'demand_forecast' | 'citizen_behavior' | 'resource_need' | 'trend_analysis';
  title: string;
  prediction: string;
  confidence: number;
  timeframe: string;
  impact: 'high' | 'medium' | 'low';
  category: string;
  data: any;
}

export const PredictiveAnalytics = ({ darkMode, analytics, requests, citizens }: PredictiveAnalyticsProps) => {
  
  const generatePredictions = (): Prediction[] => {
    const predictions: Prediction[] = [];

    // Previsão baseada em demandas atuais
    const currentRequests = requests?.length || 0;
    const resolvedRequests = requests?.filter(r => r.status === 'resolvido').length || 0;
    const urgentRequests = requests?.filter(r => r.priority === 'alta').length || 0;
    
    if (currentRequests > 0) {
      const nextMonthPrediction = Math.round(currentRequests * 1.15); // 15% de crescimento estimado
      
      predictions.push({
        id: '1',
        type: 'demand_forecast',
        title: 'Previsão de Demandas - Próximo Mês',
        prediction: `Estimativa de ${nextMonthPrediction} novas demandas baseado no volume atual`,
        confidence: 75,
        timeframe: '30 dias',
        impact: nextMonthPrediction > currentRequests * 1.3 ? 'high' : 'medium',
        category: 'Operacional',
        data: {
          current: currentRequests,
          predicted: nextMonthPrediction,
          growth: '15%'
        }
      });
    }

    // Análise de comportamento baseada em dados reais
    const totalCitizens = citizens?.length || 0;
    const activeCitizens = citizens?.filter(c => c.status === 'active').length || 0;
    
    if (totalCitizens > 0) {
      const engagementRate = (activeCitizens / totalCitizens) * 100;
      
      predictions.push({
        id: '2',
        type: 'citizen_behavior',
        title: 'Análise de Engajamento',
        prediction: `${engagementRate.toFixed(1)}% dos cidadãos estão ativos. ${engagementRate > 70 ? 'Excelente engajamento!' : 'Oportunidade de melhorar engajamento.'}`,
        confidence: 85,
        timeframe: '60 dias',
        impact: engagementRate > 70 ? 'high' : 'medium',
        category: 'Político',
        data: {
          total_citizens: totalCitizens,
          active_citizens: activeCitizens,
          engagement_rate: `${engagementRate.toFixed(1)}%`
        }
      });
    }

    // Previsão de necessidade de recursos baseada em carga de trabalho
    if (currentRequests > 0) {
      const resolutionRate = (resolvedRequests / currentRequests) * 100;
      const workload = currentRequests - resolvedRequests;
      
      predictions.push({
        id: '3',
        type: 'resource_need',
        title: 'Análise de Carga de Trabalho',
        prediction: `${workload} demandas pendentes com taxa de resolução de ${resolutionRate.toFixed(1)}%. ${workload > 20 ? 'Considere expandir equipe.' : 'Carga de trabalho controlada.'}`,
        confidence: 90,
        timeframe: '45 dias',
        impact: workload > 20 ? 'high' : 'low',
        category: 'Recursos Humanos',
        data: {
          pending_requests: workload,
          resolution_rate: `${resolutionRate.toFixed(1)}%`,
          recommendation: workload > 20 ? 'Expandir equipe' : 'Manter equipe atual'
        }
      });
    }

    // Análise de tendências por prioridade
    if (urgentRequests > 0) {
      const urgencyRate = (urgentRequests / currentRequests) * 100;
      
      predictions.push({
        id: '4',
        type: 'trend_analysis',
        title: 'Tendência de Urgência',
        prediction: `${urgencyRate.toFixed(1)}% das demandas são urgentes (${urgentRequests}/${currentRequests}). ${urgencyRate > 30 ? 'Alto nível de urgência requer atenção.' : 'Nível de urgência controlado.'}`,
        confidence: 80,
        timeframe: '21 dias',
        impact: urgencyRate > 30 ? 'high' : 'medium',
        category: 'Estratégico',
        data: {
          urgent_requests: urgentRequests,
          total_requests: currentRequests,
          urgency_rate: `${urgencyRate.toFixed(1)}%`
        }
      });
    }

    return predictions;
  };

  const predictions = generatePredictions();

  const getPredictionIcon = (type: string) => {
    switch (type) {
      case 'demand_forecast': return TrendingUp;
      case 'citizen_behavior': return Users;
      case 'resource_need': return AlertCircle;
      case 'trend_analysis': return Calendar;
      default: return TrendingUp;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return darkMode ? 'text-red-400 bg-red-900/30' : 'text-red-600 bg-red-50';
      case 'medium': return darkMode ? 'text-yellow-400 bg-yellow-900/30' : 'text-yellow-600 bg-yellow-50';
      case 'low': return darkMode ? 'text-green-400 bg-green-900/30' : 'text-green-600 bg-green-50';
      default: return darkMode ? 'text-gray-400 bg-gray-700' : 'text-gray-600 bg-gray-50';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 85) return darkMode ? 'text-green-400' : 'text-green-600';
    if (confidence >= 70) return darkMode ? 'text-yellow-400' : 'text-yellow-600';
    return darkMode ? 'text-red-400' : 'text-red-600';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className={`text-xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
          Análise Preditiva
        </h2>
        <Badge className={`${darkMode ? 'bg-blue-900/30 text-blue-400' : 'bg-blue-100 text-blue-700'}`}>
          Baseado em Dados Reais
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {predictions.length === 0 ? (
          <div className={`col-span-2 text-center py-12 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            <TrendingUp className="w-12 h-12 mx-auto mb-4" />
            <p>Dados insuficientes para gerar previsões. Adicione mais demandas e cidadãos.</p>
          </div>
        ) : (
          predictions.map((prediction) => {
            const Icon = getPredictionIcon(prediction.type);
            
            return (
              <Card key={prediction.id} className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <Icon className={`w-5 h-5 ${darkMode ? 'text-blue-400' : 'text-blue-600'}`} />
                      <CardTitle className={`text-sm ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                        {prediction.title}
                      </CardTitle>
                    </div>
                    <Badge className={`text-xs ${getImpactColor(prediction.impact)}`}>
                      {prediction.impact}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    {prediction.prediction}
                  </p>
                  
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center space-x-4">
                      <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        Prazo: {prediction.timeframe}
                      </span>
                      <span className={`px-2 py-1 rounded ${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'}`}>
                        {prediction.category}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <CheckCircle className={`w-3 h-3 ${getConfidenceColor(prediction.confidence)}`} />
                      <span className={`font-medium ${getConfidenceColor(prediction.confidence)}`}>
                        {prediction.confidence}%
                      </span>
                    </div>
                  </div>

                  {prediction.data && (
                    <div className={`mt-3 p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        {Object.entries(prediction.data).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                              {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                            </span>
                            <span className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                              {typeof value === 'number' ? value.toLocaleString() : value}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })
        )}
      </div>
    </div>
  );
};