hoistPattern:
  - '*'
hoistedDependencies:
  react@19.1.1:
    react: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.10.0
pendingBuilds: []
prunedAt: Mon, 01 Sep 2025 20:21:47 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\OneDrive\Documentos\Projetos\www\promandato\node_modules\.pnpm
virtualStoreDirMaxLength: 60
