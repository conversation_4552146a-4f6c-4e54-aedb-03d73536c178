import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();
    
    if (!email) {
      return NextResponse.json({ 
        success: false, 
        error: 'Email é obrigatório' 
      }, { status: 400 });
    }
    
    console.log('Confirmando usuário:', email);
    
    // Buscar o usuário pelo email
    const { data: users, error: listError } = await supabase.auth.admin.listUsers();
    
    if (listError) {
      return NextResponse.json({ 
        success: false, 
        error: listError.message 
      }, { status: 400 });
    }
    
    const user = users.users.find(u => u.email === email);
    
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Usu<PERSON>rio não encontrado' 
      }, { status: 404 });
    }
    
    // Confirmar o usuário
    const { data, error } = await supabase.auth.admin.updateUserById(
      user.id,
      { 
        email_confirm: true,
        email_confirmed_at: new Date().toISOString()
      }
    );
    
    if (error) {
      return NextResponse.json({ 
        success: false, 
        error: error.message 
      }, { status: 400 });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: `Usuário ${email} confirmado com sucesso`,
      user: {
        id: data.user.id,
        email: data.user.email,
        confirmed: true
      }
    });
    
  } catch (error) {
    console.error('Erro ao confirmar usuário:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 });
  }
}

// Confirmar todos os usuários não confirmados
export async function PUT(request: NextRequest) {
  try {
    const { data: users, error: listError } = await supabase.auth.admin.listUsers();
    
    if (listError) {
      return NextResponse.json({ 
        success: false, 
        error: listError.message 
      }, { status: 400 });
    }
    
    const unconfirmedUsers = users.users.filter(u => !u.email_confirmed_at);
    const results = [];
    
    for (const user of unconfirmedUsers) {
      try {
        const { data, error } = await supabase.auth.admin.updateUserById(
          user.id,
          { 
            email_confirm: true,
            email_confirmed_at: new Date().toISOString()
          }
        );
        
        if (error) {
          results.push({ email: user.email, success: false, error: error.message });
        } else {
          results.push({ email: user.email, success: true });
        }
      } catch (err) {
        results.push({ 
          email: user.email, 
          success: false, 
          error: err instanceof Error ? err.message : 'Erro desconhecido' 
        });
      }
    }
    
    return NextResponse.json({ 
      success: true, 
      message: `Processados ${unconfirmedUsers.length} usuários`,
      results
    });
    
  } catch (error) {
    console.error('Erro ao confirmar usuários:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno' 
    }, { status: 500 });
  }
}
