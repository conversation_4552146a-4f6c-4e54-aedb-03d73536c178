"use client";
import { useState } from 'react';
import { X, Mail, Phone, MapPin, Calendar, User, Facebook, Instagram, Twitter, Linkedin, MessageCircle, Tag, Edit, Save, Trash2, Search, Plus } from 'lucide-react';
import { citizenService } from '@/services/citizenService';
import { cepService } from '@/services/cepService';

interface Citizen {
  id: number;
  name: string;
  email: string;
  phone: string;
  cpf: string;
  birthDate: string;
  address: {
    street: string;
    number: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
  };
  socialMedia: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
    whatsapp?: string;
  };
  category: string;
  tags: string[];
  notes: string;
  lastContact: string;
  status: 'active' | 'inactive' | 'blocked';
}

interface CitizenDetailsModalProps {
  citizen: Citizen | null;
  isOpen: boolean;
  onClose: () => void;
  darkMode: boolean;
  onUpdate?: () => void;
}

export const CitizenDetailsModal = ({ citizen, isOpen, onClose, darkMode, onUpdate }: CitizenDetailsModalProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingCEP, setLoadingCEP] = useState(false);
  const [formData, setFormData] = useState<Citizen | null>(null);
  const [newTag, setNewTag] = useState('');

  if (!isOpen || !citizen) return null;

  // Initialize form data when entering edit mode
  const handleEditClick = () => {
    setFormData({ ...citizen });
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setFormData(null);
    setIsEditing(false);
  };

  const handleSave = async () => {
    if (!formData) return;
    
    setLoading(true);
    try {
      await citizenService.update(formData.id, {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        cpf: formData.cpf,
        birth_date: formData.birthDate,
        address: formData.address,
        social_media: formData.socialMedia,
        category: formData.category,
        tags: formData.tags,
        notes: formData.notes,
        status: formData.status
      });
      
      setIsEditing(false);
      onUpdate?.();
      alert('Contato atualizado com sucesso!');
    } catch (error) {
      console.error('Erro ao atualizar:', error);
      alert('Erro ao atualizar contato');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Tem certeza que deseja excluir este contato?')) return;
    
    setLoading(true);
    try {
      await citizenService.delete(citizen.id);
      onUpdate?.();
      onClose();
      alert('Contato excluído com sucesso!');
    } catch (error) {
      console.error('Erro ao excluir:', error);
      alert('Erro ao excluir contato');
    } finally {
      setLoading(false);
    }
  };

  const addTag = () => {
    if (newTag.trim() && formData && !formData.tags.includes(newTag.trim())) {
      setFormData({
        ...formData,
        tags: [...formData.tags, newTag.trim()]
      });
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    if (formData) {
      setFormData({
        ...formData,
        tags: formData.tags.filter(tag => tag !== tagToRemove)
      });
    }
  };

  const currentData = isEditing ? formData : citizen;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-yellow-100 text-yellow-800';
      case 'blocked': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className={`max-w-2xl w-full max-h-[90vh] overflow-y-auto rounded-lg ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white'} border`}>
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-lg">
              {currentData?.name.charAt(0)}
            </div>
            <div>
              <h2 className={`text-xl font-semibold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
                {currentData?.name}
              </h2>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(currentData?.status || 'active')}`}>
                {currentData?.status}
              </span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {!isEditing ? (
              <>
                <button 
                  onClick={handleEditClick}
                  className={`p-2 rounded-full ${darkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-600'}`}
                >
                  <Edit className="w-5 h-5" />
                </button>
                <button 
                  onClick={handleDelete}
                  disabled={loading}
                  className={`p-2 rounded-full ${darkMode ? 'hover:bg-red-700 text-red-400' : 'hover:bg-red-100 text-red-600'} disabled:opacity-50`}
                >
                  <Trash2 className="w-5 h-5" />
                </button>
                <button 
                  onClick={onClose}
                  className={`p-2 rounded-full ${darkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-600'}`}
                >
                  <X className="w-5 h-5" />
                </button>
              </>
            ) : (
              <>
                <button 
                  onClick={handleSave}
                  disabled={loading}
                  className={`p-2 rounded-full ${darkMode ? 'hover:bg-green-700 text-green-400' : 'hover:bg-green-100 text-green-600'} disabled:opacity-50`}
                >
                  <Save className="w-5 h-5" />
                </button>
                <button 
                  onClick={handleCancelEdit}
                  disabled={loading}
                  className={`p-2 rounded-full ${darkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-600'} disabled:opacity-50`}
                >
                  <X className="w-5 h-5" />
                </button>
              </>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Informações Básicas */}
          <div>
            <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
              Informações Básicas
            </h3>
            {isEditing ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Nome</label>
                  <input
                    type="text"
                    value={formData?.name || ''}
                    onChange={(e) => setFormData(prev => prev ? { ...prev, name: e.target.value } : null)}
                    className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                  />
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Email</label>
                  <input
                    type="email"
                    value={formData?.email || ''}
                    onChange={(e) => setFormData(prev => prev ? { ...prev, email: e.target.value } : null)}
                    className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                  />
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Telefone</label>
                  <input
                    type="tel"
                    value={formData?.phone || ''}
                    onChange={(e) => setFormData(prev => prev ? { ...prev, phone: e.target.value } : null)}
                    className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                  />
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>CPF</label>
                  <input
                    type="text"
                    value={formData?.cpf || ''}
                    onChange={(e) => setFormData(prev => prev ? { ...prev, cpf: e.target.value } : null)}
                    className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                  />
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Data de Nascimento</label>
                  <input
                    type="date"
                    value={formData?.birthDate || ''}
                    onChange={(e) => setFormData(prev => prev ? { ...prev, birthDate: e.target.value } : null)}
                    className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                  />
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Categoria</label>
                  <select
                    value={formData?.category || ''}
                    onChange={(e) => setFormData(prev => prev ? { ...prev, category: e.target.value } : null)}
                    className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                  >
                    <option value="eleitor">Eleitor</option>
                    <option value="liderança">Liderança</option>
                    <option value="empresário">Empresário</option>
                    <option value="funcionário público">Funcionário Público</option>
                    <option value="estudante">Estudante</option>
                    <option value="apoiador">Apoiador</option>
                  </select>
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Status</label>
                  <select
                    value={formData?.status || ''}
                    onChange={(e) => setFormData(prev => prev ? { ...prev, status: e.target.value as any } : null)}
                    className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                  >
                    <option value="active">Ativo</option>
                    <option value="inactive">Inativo</option>
                    <option value="blocked">Bloqueado</option>
                  </select>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-3">
                  <Mail className={`w-5 h-5 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                  <div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Email</p>
                    <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{currentData?.email}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className={`w-5 h-5 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                  <div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Telefone</p>
                    <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{currentData?.phone}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <User className={`w-5 h-5 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                  <div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>CPF</p>
                    <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{currentData?.cpf || 'Não informado'}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Calendar className={`w-5 h-5 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                  <div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Data de Nascimento</p>
                    <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                      {currentData?.birthDate ? formatDate(currentData.birthDate) : 'Não informado'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Tag className={`w-5 h-5 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                  <div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Categoria</p>
                    <p className={`capitalize ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{currentData?.category}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Calendar className={`w-5 h-5 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                  <div>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Último Contato</p>
                    <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{formatDate(currentData?.lastContact || '')}</p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Endereço */}
          <div>
            <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
              Endereço
            </h3>
            {isEditing ? (
              <div className="space-y-4">
                <div className="max-w-xs">
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>CEP</label>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={formData?.address?.zipCode || ''}
                      onChange={(e) => setFormData(prev => prev ? {
                        ...prev,
                        address: { ...prev.address, zipCode: e.target.value }
                      } : null)}
                      className={`flex-1 px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                      placeholder="00000-000"
                    />
                    <button
                      type="button"
                      onClick={async () => {
                        if (!formData?.address?.zipCode) return;
                        setLoadingCEP(true);
                        const cepData = await cepService.buscarCEP(formData.address.zipCode);
                        if (cepData && formData) {
                          setFormData({
                            ...formData,
                            address: {
                              ...formData.address,
                              street: cepData.logradouro,
                              neighborhood: cepData.bairro,
                              city: cepData.localidade,
                              state: cepData.uf,
                              zipCode: cepService.formatCEP(cepData.cep)
                            }
                          });
                        }
                        setLoadingCEP(false);
                      }}
                      disabled={loadingCEP}
                      className={`px-3 py-2 rounded-md ${darkMode ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-blue-600 hover:bg-blue-700 text-white'} disabled:opacity-50`}
                    >
                      {loadingCEP ? '...' : <Search className="w-4 h-4" />}
                    </button>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Cidade</label>
                    <input
                      type="text"
                      value={formData?.address?.city || ''}
                      onChange={(e) => setFormData(prev => prev ? {
                        ...prev,
                        address: { ...prev.address, city: e.target.value }
                      } : null)}
                      className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Estado</label>
                    <input
                      type="text"
                      value={formData?.address?.state || ''}
                      onChange={(e) => setFormData(prev => prev ? {
                        ...prev,
                        address: { ...prev.address, state: e.target.value }
                      } : null)}
                      className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                      maxLength={2}
                      placeholder="SP"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="md:col-span-2">
                    <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Rua</label>
                    <input
                      type="text"
                      value={formData?.address?.street || ''}
                      onChange={(e) => setFormData(prev => prev ? {
                        ...prev,
                        address: { ...prev.address, street: e.target.value }
                      } : null)}
                      className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Número</label>
                    <input
                      type="text"
                      value={formData?.address?.number || ''}
                      onChange={(e) => setFormData(prev => prev ? {
                        ...prev,
                        address: { ...prev.address, number: e.target.value }
                      } : null)}
                      className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                    />
                  </div>
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Bairro</label>
                  <input
                    type="text"
                    value={formData?.address?.neighborhood || ''}
                    onChange={(e) => setFormData(prev => prev ? {
                      ...prev,
                      address: { ...prev.address, neighborhood: e.target.value }
                    } : null)}
                    className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                  />
                </div>
              </div>
            ) : (
              currentData?.address && (currentData.address.street || currentData.address.city) && (
                <div className="flex items-start space-x-3">
                  <MapPin className={`w-5 h-5 mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                  <div>
                    <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                      {currentData.address.street && currentData.address.number 
                        ? `${currentData.address.street}, ${currentData.address.number}`
                        : currentData.address.street || 'Rua não informada'
                      }
                    </p>
                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {currentData.address.neighborhood && `${currentData.address.neighborhood}, `}
                      {currentData.address.city || 'Cidade não informada'}
                      {currentData.address.state && ` - ${currentData.address.state}`}
                    </p>
                    {currentData.address.zipCode && (
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        CEP: {currentData.address.zipCode}
                      </p>
                    )}
                  </div>
                </div>
              )
            )}
          </div>

          {/* Redes Sociais */}
          {citizen.socialMedia && Object.values(citizen.socialMedia).some(value => value) && (
            <div>
              <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                Redes Sociais
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {citizen.socialMedia.facebook && (
                  <div className="flex items-center space-x-3">
                    <Facebook className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Facebook</p>
                      <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{citizen.socialMedia.facebook}</p>
                    </div>
                  </div>
                )}
                {citizen.socialMedia.instagram && (
                  <div className="flex items-center space-x-3">
                    <Instagram className="w-5 h-5 text-pink-600" />
                    <div>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Instagram</p>
                      <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{citizen.socialMedia.instagram}</p>
                    </div>
                  </div>
                )}
                {citizen.socialMedia.twitter && (
                  <div className="flex items-center space-x-3">
                    <Twitter className="w-5 h-5 text-blue-400" />
                    <div>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Twitter</p>
                      <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{citizen.socialMedia.twitter}</p>
                    </div>
                  </div>
                )}
                {citizen.socialMedia.linkedin && (
                  <div className="flex items-center space-x-3">
                    <Linkedin className="w-5 h-5 text-blue-700" />
                    <div>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>LinkedIn</p>
                      <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{citizen.socialMedia.linkedin}</p>
                    </div>
                  </div>
                )}
                {citizen.socialMedia.whatsapp && (
                  <div className="flex items-center space-x-3">
                    <MessageCircle className="w-5 h-5 text-green-600" />
                    <div>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>WhatsApp</p>
                      <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{citizen.socialMedia.whatsapp}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Redes Sociais */}
          <div>
            <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
              Redes Sociais
            </h3>
            {isEditing ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Facebook</label>
                  <input
                    type="text"
                    value={formData?.socialMedia?.facebook || ''}
                    onChange={(e) => setFormData(prev => prev ? {
                      ...prev,
                      socialMedia: { ...prev.socialMedia, facebook: e.target.value }
                    } : null)}
                    className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                    placeholder="@usuario ou URL"
                  />
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Instagram</label>
                  <input
                    type="text"
                    value={formData?.socialMedia?.instagram || ''}
                    onChange={(e) => setFormData(prev => prev ? {
                      ...prev,
                      socialMedia: { ...prev.socialMedia, instagram: e.target.value }
                    } : null)}
                    className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                    placeholder="@usuario ou URL"
                  />
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>WhatsApp</label>
                  <input
                    type="text"
                    value={formData?.socialMedia?.whatsapp || ''}
                    onChange={(e) => setFormData(prev => prev ? {
                      ...prev,
                      socialMedia: { ...prev.socialMedia, whatsapp: e.target.value }
                    } : null)}
                    className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                    placeholder="Número com DDD"
                  />
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>LinkedIn</label>
                  <input
                    type="text"
                    value={formData?.socialMedia?.linkedin || ''}
                    onChange={(e) => setFormData(prev => prev ? {
                      ...prev,
                      socialMedia: { ...prev.socialMedia, linkedin: e.target.value }
                    } : null)}
                    className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                    placeholder="URL do perfil"
                  />
                </div>
              </div>
            ) : (
              currentData?.socialMedia && Object.values(currentData.socialMedia).some(value => value) && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {currentData.socialMedia.facebook && (
                    <div className="flex items-center space-x-3">
                      <Facebook className="w-5 h-5 text-blue-600" />
                      <div>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Facebook</p>
                        <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{currentData.socialMedia.facebook}</p>
                      </div>
                    </div>
                  )}
                  {currentData.socialMedia.instagram && (
                    <div className="flex items-center space-x-3">
                      <Instagram className="w-5 h-5 text-pink-600" />
                      <div>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Instagram</p>
                        <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{currentData.socialMedia.instagram}</p>
                      </div>
                    </div>
                  )}
                  {currentData.socialMedia.twitter && (
                    <div className="flex items-center space-x-3">
                      <Twitter className="w-5 h-5 text-blue-400" />
                      <div>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Twitter</p>
                        <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{currentData.socialMedia.twitter}</p>
                      </div>
                    </div>
                  )}
                  {currentData.socialMedia.linkedin && (
                    <div className="flex items-center space-x-3">
                      <Linkedin className="w-5 h-5 text-blue-700" />
                      <div>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>LinkedIn</p>
                        <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{currentData.socialMedia.linkedin}</p>
                      </div>
                    </div>
                  )}
                  {currentData.socialMedia.whatsapp && (
                    <div className="flex items-center space-x-3">
                      <MessageCircle className="w-5 h-5 text-green-600" />
                      <div>
                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>WhatsApp</p>
                        <p className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{currentData.socialMedia.whatsapp}</p>
                      </div>
                    </div>
                  )}
                </div>
              )
            )}
          </div>

          {/* Tags */}
          <div>
            <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
              Tags
            </h3>
            {isEditing ? (
              <div>
                <div className="flex gap-2 mb-3">
                  <input
                    type="text"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Adicionar tag..."
                    className={`flex-1 px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  />
                  <button
                    type="button"
                    onClick={addTag}
                    className={`px-3 py-2 rounded-md ${darkMode ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-blue-600 hover:bg-blue-700 text-white'}`}
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData?.tags?.map((tag, index) => (
                    <span 
                      key={index} 
                      className={`px-3 py-1 rounded-full text-sm flex items-center gap-1 ${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-700'}`}
                    >
                      {tag}
                      <X className="w-3 h-3 cursor-pointer" onClick={() => removeTag(tag)} />
                    </span>
                  ))}
                </div>
              </div>
            ) : (
              currentData?.tags && currentData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {currentData.tags.map((tag, index) => (
                    <span 
                      key={index} 
                      className={`px-3 py-1 rounded-full text-sm ${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-700'}`}
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )
            )}
          </div>

          {/* Observações */}
          <div>
            <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
              Observações
            </h3>
            {isEditing ? (
              <textarea
                value={formData?.notes || ''}
                onChange={(e) => setFormData(prev => prev ? { ...prev, notes: e.target.value } : null)}
                className={`w-full px-3 py-2 border rounded-md ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                rows={4}
                placeholder="Adicione observações sobre este contato..."
              />
            ) : (
              currentData?.notes && (
                <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                  <p className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>{currentData.notes}</p>
                </div>
              )
            )}
          </div>
        </div>
      </div>
    </div>
  );
};