interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface AIContext {
  page: string;
  data?: any;
  userRole?: string;
}

export const aiService = {
  async generateResponse(message: string, context: AIContext): Promise<string> {
    try {
      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          page: context.page || 'dashboard',
          messages: [
            { role: 'user', content: message }
          ]
        })
      });

      if (!response.ok) {
        throw new Error('Erro na API de IA');
      }

      const data = await response.json();
      return data.response;
    } catch (error) {
      console.error('Erro no serviço de IA:', error);
      return this.getLocalFallback(message, context);
    }
  },

  getLocalFallback(message: string, context: AIContext): string {
    const responses = {
      dashboard: 'Com base nos dados do dashboard, sugiro focar nas demandas de alta prioridade e acompanhar as métricas de engajamento.',
      citizens: 'Para melhor atendimento aos cidadãos, recomendo categorizar as demandas por tema e criar um sistema de acompanhamento.',
      requests: 'Priorize as demandas por urgência e impacto. Considere criar grupos de trabalho para temas recorrentes.',
      agenda: 'Otimize sua agenda reservando blocos de tempo para diferentes tipos de atividades e mantenha espaço para emergências.',
      projects: 'Acompanhe o progresso dos projetos com marcos claros e comunique regularmente os avanços à equipe.',
      communications: 'Mantenha consistência na comunicação e adapte a linguagem para cada canal e público-alvo.',
      analytics: 'Analise as tendências dos dados para identificar oportunidades de melhoria e áreas que precisam de atenção.',
      team: 'Distribua as tarefas conforme as competências da equipe e mantenha comunicação regular sobre objetivos.'
    };
    
    return responses[context.page as keyof typeof responses] || 'Desculpe, não consegui processar sua solicitação no momento. Tente novamente.';
  }
};