interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface AIContext {
  page: string;
  data?: any;
  userRole?: string;
}

export const aiService = {
  async generateResponse(message: string, context: AIContext): Promise<string> {
    try {
      const response = await fetch('/api/ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          page: context.page || 'dashboard',
          messages: [
            { role: 'user', content: message }
          ]
        })
      });

      if (!response.ok) {
        throw new Error('Erro na API de IA');
      }

      const data = await response.json();
      return data.response;
    } catch (error) {
      console.error('Erro no serviço de IA:', error);
      return aiService.getLocalFallback(message, context);
    }
  },

  getLocalFallback(message: string, context: AIContext): string {
    return 'Des<PERSON>lpe, não consegui processar sua solicitação no momento. Conforme você adicionar dados reais ao sistema, poderei fornecer análises mais específicas. Tente novamente.';
  }
};