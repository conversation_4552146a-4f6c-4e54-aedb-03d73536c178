"use client";
import { useState } from 'react';
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Textarea } from "@/app/components/ui/textarea";
import { socialPostService } from '@/services/socialPostService';

interface NewPostModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  darkMode: boolean;
  onSave?: () => void;
}

export const NewPostModal = ({ isOpen, setIsOpen, darkMode, onSave }: NewPostModalProps) => {
    const [formData, setFormData] = useState({
        platform: 'Facebook',
        content: '',
        hashtags: ''
    });
    const [loading, setLoading] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        try {
            await socialPostService.create({
                platform: formData.platform as 'Facebook' | 'Instagram' | 'Twitter' | 'LinkedIn',
                content: formData.content,
                hashtags: formData.hashtags.split(' ').filter(tag => tag.trim()),
                status: 'published',
                published_date: new Date().toISOString(),
                likes: 0,
                comments: 0,
                shares: 0,
                mentions: [],
                media_urls: []
            });
            setIsOpen(false);
            setFormData({ platform: 'Facebook', content: '', hashtags: '' });
            onSave?.();
        } catch (error) {
            console.error('Error creating post:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className={`${darkMode ? 'bg-gray-800 border-gray-700' : ''}`}>
                <DialogHeader>
                    <DialogTitle className={`${darkMode ? 'text-white' : ''}`}>Nova Publicação</DialogTitle>
                    <DialogDescription className={`${darkMode ? 'text-gray-400' : ''}`}>
                        Crie uma nova publicação para as redes sociais.
                    </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="platform" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                                Plataforma
                            </Label>
                            <select
                                id="platform"
                                value={formData.platform}
                                onChange={(e) => setFormData(prev => ({ ...prev, platform: e.target.value }))}
                                className={`col-span-3 px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300'}`}
                                required
                            >
                                <option value="Facebook">Facebook</option>
                                <option value="Instagram">Instagram</option>
                                <option value="Twitter">Twitter</option>
                                <option value="LinkedIn">LinkedIn</option>
                            </select>
                        </div>
                        <div className="grid grid-cols-4 items-start gap-4">
                            <Label htmlFor="content" className={`text-right pt-2 ${darkMode ? 'text-gray-300' : ''}`}>
                                Conteúdo
                            </Label>
                            <Textarea
                                id="content"
                                value={formData.content}
                                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                                placeholder="Digite o conteúdo da publicação..."
                                className={`col-span-3 ${darkMode ? 'text-gray-100 bg-gray-700 border-gray-600' : ''}`}
                                rows={4}
                                required
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="hashtags" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                                Hashtags
                            </Label>
                            <Input
                                id="hashtags"
                                value={formData.hashtags}
                                onChange={(e) => setFormData(prev => ({ ...prev, hashtags: e.target.value }))}
                                placeholder="#exemplo #hashtag"
                                className={`col-span-3 ${darkMode ? 'text-gray-100' : ''}`}
                            />
                        </div>
                    </div>
                </form>
                <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                        Cancelar
                    </Button>
                    <Button type="submit" onClick={handleSubmit} disabled={loading}>
                        {loading ? 'Publicando...' : 'Publicar'}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};