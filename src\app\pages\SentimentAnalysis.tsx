"use client";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { Progress } from "@/app/components/ui/progress";
import { <PERSON>, <PERSON><PERSON>, <PERSON>own, <PERSON><PERSON>dingUp, MessageSquare, Heart, Al<PERSON>gle, FileText } from 'lucide-react';

interface SentimentAnalysisProps {
  darkMode: boolean;
  socialPosts?: any[];
  requests?: any[];
}

interface SentimentData {
  overall: {
    positive: number;
    neutral: number;
    negative: number;
    score: number;
  };
  bySource: {
    social_media: { positive: number; neutral: number; negative: number; };
    requests: { positive: number; neutral: number; negative: number; };
    system: { positive: number; neutral: number; negative: number; };
  };
  trends: {
    week: number;
    month: number;
  };
  keywords: {
    positive: string[];
    negative: string[];
  };
  alerts: {
    id: string;
    type: 'positive' | 'negative' | 'neutral';
    message: string;
    source: string;
    timestamp: Date;
  }[];
}

export const SentimentAnalysis = ({ darkMode, socialPosts = [], requests = [] }: SentimentAnalysisProps) => {
  
  const generateSentimentData = (): SentimentData => {
    // Análise detalhada das publicações sociais
    const totalLikes = socialPosts?.reduce((sum, post) => sum + (post.likes || 0), 0) || 0;
    const totalComments = socialPosts?.reduce((sum, post) => sum + (post.comments || 0), 0) || 0;
    const totalShares = socialPosts?.reduce((sum, post) => sum + (post.shares || 0), 0) || 0;
    const totalEngagement = totalLikes + totalComments + totalShares;
    const avgEngagement = socialPosts?.length > 0 ? totalEngagement / socialPosts.length : 0;
    
    // Análise avançada das demandas
    const resolvedRequests = requests?.filter(r => r.status === 'resolvido').length || 0;
    const pendingRequests = requests?.filter(r => r.status === 'pendente').length || 0;
    const urgentRequests = requests?.filter(r => r.priority === 'alta').length || 0;
    const totalRequests = requests?.length || 1;
    
    // Cálculo inteligente de sentimentos baseado em múltiplos fatores
    const resolutionRate = (resolvedRequests / totalRequests) * 100;
    const urgencyRate = (urgentRequests / totalRequests) * 100;
    const engagementScore = Math.min(100, (avgEngagement / 20) * 100); // Normalizado para 0-100
    
    // Sentimento das redes sociais (baseado em engajamento e tipo de interação)
    const socialPositive = socialPosts?.length > 0 ? 
      Math.min(85, Math.max(20, 
        (totalLikes * 1.0 + totalShares * 1.5 - totalComments * 0.3) / Math.max(1, socialPosts.length) * 5
      )) : 45;
    
    // Sentimento das demandas (baseado em resolução e urgência)
    const requestsPositive = Math.min(90, Math.max(15, 
      resolutionRate * 0.8 - urgencyRate * 0.5 + 20
    ));
    
    // Sentimento do sistema (baseado em eficiência operacional)
    const systemPositive = Math.min(85, Math.max(30,
      70 - (urgencyRate * 0.8) + (resolutionRate > 70 ? 15 : 0)
    ));
    
    const overallPositive = Math.round((socialPositive + requestsPositive + systemPositive) / 3);
    const overallNegative = Math.round(Math.max(8, urgencyRate * 0.6 + (resolutionRate < 50 ? 15 : 5)));
    const overallNeutral = 100 - overallPositive - overallNegative;
    
    // Palavras-chave inteligentes baseadas no contexto real
    const positiveKeywords = [];
    const negativeKeywords = [];
    
    if (resolutionRate > 70) positiveKeywords.push('eficiente', 'resolvido', 'excelente');
    if (totalLikes > totalRequests * 2) positiveKeywords.push('popular', 'aprovado', 'curtido');
    if (totalShares > 10) positiveKeywords.push('compartilhado', 'viral', 'engajamento');
    if (resolvedRequests > 10) positiveKeywords.push('produtivo', 'ativo', 'responsivo');
    
    if (urgencyRate > 30) negativeKeywords.push('urgente', 'crítico', 'prioridade');
    if (resolutionRate < 40) negativeKeywords.push('pendente', 'atrasado', 'demora');
    if (pendingRequests > totalRequests * 0.6) negativeKeywords.push('acumulado', 'sobrecarga');
    if (avgEngagement < 5 && socialPosts?.length > 0) negativeKeywords.push('baixo engajamento', 'pouca interação');
    
    // Fallbacks
    if (positiveKeywords.length === 0) positiveKeywords.push('estável', 'funcionando', 'ativo');
    if (negativeKeywords.length === 0) negativeKeywords.push('melhorar', 'atenção');
    
    // Sistema inteligente de alertas baseado em múltiplos indicadores
    const alerts = [];
    let alertId = 1;
    
    // Alertas de engajamento social
    if (avgEngagement > 30) {
      alerts.push({
        id: String(alertId++),
        type: 'positive' as const,
        message: `🚀 Engajamento excepcional: ${Math.round(avgEngagement)} interações/post (${totalEngagement} total)`,
        source: 'Redes Sociais',
        timestamp: new Date()
      });
    } else if (avgEngagement < 5 && socialPosts?.length > 3) {
      alerts.push({
        id: String(alertId++),
        type: 'negative' as const,
        message: `📉 Baixo engajamento: apenas ${Math.round(avgEngagement)} interações/post`,
        source: 'Redes Sociais',
        timestamp: new Date()
      });
    }
    
    // Alertas de demandas críticas
    if (urgencyRate > 25) {
      alerts.push({
        id: String(alertId++),
        type: 'negative' as const,
        message: `⚠️ ${urgentRequests} demandas urgentes (${urgencyRate.toFixed(1)}%) - Ação imediata necessária`,
        source: 'Sistema de Demandas',
        timestamp: new Date()
      });
    }
    
    // Alertas de performance
    if (resolutionRate > 80) {
      alerts.push({
        id: String(alertId++),
        type: 'positive' as const,
        message: `✅ Excelente performance: ${resolutionRate.toFixed(1)}% de resolução (${resolvedRequests}/${totalRequests})`,
        source: 'Sistema de Demandas',
        timestamp: new Date()
      });
    } else if (resolutionRate < 40) {
      alerts.push({
        id: String(alertId++),
        type: 'negative' as const,
        message: `🔄 Taxa de resolução baixa: ${resolutionRate.toFixed(1)}% - Revisar processos`,
        source: 'Sistema de Demandas',
        timestamp: new Date()
      });
    }
    
    // Alertas de tendências
    if (totalShares > totalLikes * 0.3) {
      alerts.push({
        id: String(alertId++),
        type: 'positive' as const,
        message: `📈 Conteúdo viral: ${totalShares} compartilhamentos indicam alta relevância`,
        source: 'Análise de Tendências',
        timestamp: new Date()
      });
    }
    
    // Alerta de dados insuficientes
    if (socialPosts?.length === 0 && requests?.length === 0) {
      alerts.push({
        id: String(alertId++),
        type: 'neutral' as const,
        message: '📊 Dados insuficientes - Adicione publicações sociais e demandas para análise completa',
        source: 'Sistema de Análise',
        timestamp: new Date()
      });
    } else if (socialPosts?.length === 0) {
      alerts.push({
        id: String(alertId++),
        type: 'neutral' as const,
        message: '📱 Sem dados de redes sociais - Conecte suas contas para análise de sentimento completa',
        source: 'Redes Sociais',
        timestamp: new Date()
      });
    }
    
    return {
      overall: {
        positive: overallPositive,
        neutral: overallNeutral,
        negative: overallNegative,
        score: (overallPositive - overallNegative) / 100
      },
      bySource: {
        social_media: { 
          positive: Math.round(socialPositive), 
          neutral: Math.round(100 - socialPositive - 10), 
          negative: 10 
        },
        requests: { 
          positive: Math.round(requestsPositive), 
          neutral: Math.round(100 - requestsPositive - (urgentRequests > 0 ? 20 : 10)), 
          negative: urgentRequests > 0 ? 20 : 10 
        },
        system: { 
          positive: Math.round(systemPositive), 
          neutral: Math.round(100 - systemPositive - 15), 
          negative: 15 
        }
      },
      trends: {
        week: (() => {
          if (socialPosts?.length === 0 && requests?.length === 0) return 0;
          const weekTrend = (resolutionRate > 60 ? 3 : -2) + (avgEngagement > 15 ? 2 : -1);
          return Math.max(-15, Math.min(15, weekTrend));
        })(),
        month: (() => {
          if (socialPosts?.length === 0 && requests?.length === 0) return 0;
          const monthTrend = (overallPositive > 60 ? 5 : -3) + (totalEngagement > 100 ? 3 : -2);
          return Math.max(-20, Math.min(20, monthTrend));
        })()
      },
      keywords: {
        positive: positiveKeywords,
        negative: negativeKeywords
      },
      alerts
    };
  };

  const sentimentData = generateSentimentData();

  const getSentimentColor = (sentiment: 'positive' | 'neutral' | 'negative') => {
    switch (sentiment) {
      case 'positive': return darkMode ? 'text-green-400' : 'text-green-600';
      case 'neutral': return darkMode ? 'text-yellow-400' : 'text-yellow-600';
      case 'negative': return darkMode ? 'text-red-400' : 'text-red-600';
    }
  };

  const getSentimentIcon = (sentiment: 'positive' | 'neutral' | 'negative') => {
    switch (sentiment) {
      case 'positive': return Smile;
      case 'neutral': return Meh;
      case 'negative': return Frown;
    }
  };

  const getSentimentBg = (sentiment: 'positive' | 'neutral' | 'negative') => {
    switch (sentiment) {
      case 'positive': return darkMode ? 'bg-green-900/30' : 'bg-green-50';
      case 'neutral': return darkMode ? 'bg-yellow-900/30' : 'bg-yellow-50';
      case 'negative': return darkMode ? 'bg-red-900/30' : 'bg-red-50';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className={`text-xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
          Análise de Sentimento
        </h2>
        <Badge className={`${
          sentimentData.overall.score > 0.3 
            ? (darkMode ? 'bg-green-900/30 text-green-400' : 'bg-green-100 text-green-700')
            : sentimentData.overall.score > -0.3
            ? (darkMode ? 'bg-yellow-900/30 text-yellow-400' : 'bg-yellow-100 text-yellow-700')
            : (darkMode ? 'bg-red-900/30 text-red-400' : 'bg-red-100 text-red-700')
        }`}>
          Score: {sentimentData.overall.score.toFixed(2)}
        </Badge>
      </div>

      {/* Visão Geral do Sentimento */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Smile className={`w-5 h-5 ${getSentimentColor('positive')}`} />
                <span className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>Positivo</span>
              </div>
              <span className={`text-2xl font-bold ${getSentimentColor('positive')}`}>
                {sentimentData.overall.positive}%
              </span>
            </div>
            <Progress value={sentimentData.overall.positive} className="h-2" />
          </CardContent>
        </Card>

        <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Meh className={`w-5 h-5 ${getSentimentColor('neutral')}`} />
                <span className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>Neutro</span>
              </div>
              <span className={`text-2xl font-bold ${getSentimentColor('neutral')}`}>
                {sentimentData.overall.neutral}%
              </span>
            </div>
            <Progress value={sentimentData.overall.neutral} className="h-2" />
          </CardContent>
        </Card>

        <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Frown className={`w-5 h-5 ${getSentimentColor('negative')}`} />
                <span className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>Negativo</span>
              </div>
              <span className={`text-2xl font-bold ${getSentimentColor('negative')}`}>
                {sentimentData.overall.negative}%
              </span>
            </div>
            <Progress value={sentimentData.overall.negative} className="h-2" />
          </CardContent>
        </Card>
      </div>

      {/* Métricas Detalhadas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MessageSquare className="w-4 h-4 text-blue-500" />
              <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Posts Sociais</span>
            </div>
            <p className={`text-xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
              {socialPosts?.length || 0}
            </p>
            <p className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
              {(socialPosts?.reduce((sum, post) => sum + (post.likes || 0) + (post.comments || 0) + (post.shares || 0), 0) || 0)} interações
            </p>
          </CardContent>
        </Card>
        
        <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="w-4 h-4 text-purple-500" />
              <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Demandas</span>
            </div>
            <p className={`text-xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
              {requests?.length || 0}
            </p>
            <p className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
              {requests?.filter(r => r.status === 'resolvido').length || 0} resolvidas
            </p>
          </CardContent>
        </Card>
        
        <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Heart className="w-4 h-4 text-red-500" />
              <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Engajamento</span>
            </div>
            <p className={`text-xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
              {socialPosts?.length > 0 ? 
                Math.round((socialPosts.reduce((sum, post) => sum + (post.likes || 0) + (post.comments || 0) + (post.shares || 0), 0) / socialPosts.length)) 
                : 0}
            </p>
            <p className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
              por post
            </p>
          </CardContent>
        </Card>
        
        <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-4 h-4 text-green-500" />
              <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Taxa Resolução</span>
            </div>
            <p className={`text-xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
              {requests?.length > 0 ? 
                Math.round((requests.filter(r => r.status === 'resolvido').length / requests.length) * 100) 
                : 0}%
            </p>
            <p className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
              eficiência
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sentimento por Fonte */}
        <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <CardHeader>
            <CardTitle className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
              Sentimento por Fonte
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(sentimentData.bySource).map(([source, data]) => {
              const sourceData = {
                'social_media': { name: 'Redes Sociais', count: socialPosts?.length || 0, icon: MessageSquare },
                'requests': { name: 'Demandas', count: requests?.length || 0, icon: FileText },
                'system': { name: 'Sistema', count: (socialPosts?.length || 0) + (requests?.length || 0), icon: TrendingUp }
              }[source];
              
              return (
                <div key={source} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <sourceData.icon className="w-4 h-4 text-gray-500" />
                      <span className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        {sourceData.name}
                      </span>
                      <span className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                        ({sourceData.count})
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-xs">
                      <span className={getSentimentColor('positive')}>{data.positive}%</span>
                      <span className={getSentimentColor('neutral')}>{data.neutral}%</span>
                      <span className={getSentimentColor('negative')}>{data.negative}%</span>
                    </div>
                  </div>
                  <div className="flex h-2 rounded-full overflow-hidden">
                    <div 
                      className="bg-green-500" 
                      style={{ width: `${data.positive}%` }}
                    ></div>
                    <div 
                      className="bg-yellow-500" 
                      style={{ width: `${data.neutral}%` }}
                    ></div>
                    <div 
                      className="bg-red-500" 
                      style={{ width: `${data.negative}%` }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </CardContent>
        </Card>

        {/* Tendências e Palavras-chave */}
        <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <CardHeader>
            <CardTitle className={`${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
              Tendências e Palavras-chave
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className={`flex items-center justify-between p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className="flex items-center space-x-2">
                <TrendingUp className={`w-4 h-4 ${sentimentData.trends.week > 0 ? 'text-green-500' : 'text-red-500'}`} />
                <span className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Esta Semana</span>
              </div>
              <span className={`font-bold ${sentimentData.trends.week > 0 ? 'text-green-500' : 'text-red-500'}`}>
                {sentimentData.trends.week > 0 ? '+' : ''}{sentimentData.trends.week}%
              </span>
            </div>
            
            <div className={`flex items-center justify-between p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className="flex items-center space-x-2">
                <TrendingUp className={`w-4 h-4 ${sentimentData.trends.month > 0 ? 'text-green-500' : 'text-red-500'}`} />
                <span className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Este Mês</span>
              </div>
              <span className={`font-bold ${sentimentData.trends.month > 0 ? 'text-green-500' : 'text-red-500'}`}>
                {sentimentData.trends.month > 0 ? '+' : ''}{sentimentData.trends.month}%
              </span>
            </div>

            <div className="space-y-4">
              <div>
                <h4 className={`font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Palavras-chave Positivas
                </h4>
                <div className="flex flex-wrap gap-1">
                  {sentimentData.keywords.positive.map((keyword, index) => (
                    <Badge key={index} className={`text-xs ${getSentimentBg('positive')} ${getSentimentColor('positive')} border-0`}>
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <h4 className={`font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Palavras-chave Negativas
                </h4>
                <div className="flex flex-wrap gap-1">
                  {sentimentData.keywords.negative.map((keyword, index) => (
                    <Badge key={index} className={`text-xs ${getSentimentBg('negative')} ${getSentimentColor('negative')} border-0`}>
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Alertas de Sentimento */}
        <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <CardHeader>
            <CardTitle className={`flex items-center space-x-2 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
              <AlertTriangle className="w-5 h-5" />
              <span>Alertas de Sentimento</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 max-h-80 overflow-y-auto">
            {sentimentData.alerts.map((alert) => {
              const Icon = getSentimentIcon(alert.type);
              
              return (
                <div key={alert.id} className={`flex items-start space-x-3 p-3 rounded-lg ${getSentimentBg(alert.type)}`}>
                  <Icon className={`w-5 h-5 mt-0.5 ${getSentimentColor(alert.type)}`} />
                  <div className="flex-1">
                    <p className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                      {alert.message}
                    </p>
                    <div className="flex items-center space-x-2 mt-1 text-xs">
                      <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {alert.source}
                      </span>
                      <span className={`${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>•</span>
                      <span className={`${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                        {alert.timestamp.toLocaleString('pt-BR')}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </CardContent>
        </Card>

        {/* Insights Inteligentes */}
        <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <CardHeader>
            <CardTitle className={`flex items-center space-x-2 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
              <TrendingUp className="w-5 h-5" />
              <span>Insights Inteligentes</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {(() => {
              const insights = [];
              const resolutionRate = requests?.length > 0 ? (requests.filter(r => r.status === 'resolvido').length / requests.length) * 100 : 0;
              const avgEngagement = socialPosts?.length > 0 ? 
                (socialPosts.reduce((sum, post) => sum + (post.likes || 0) + (post.comments || 0) + (post.shares || 0), 0) / socialPosts.length) : 0;
              
              if (resolutionRate > 75) {
                insights.push({
                  icon: '🎯',
                  title: 'Excelente Eficiência',
                  description: `Sua taxa de resolução de ${resolutionRate.toFixed(1)}% está acima da média. Continue priorizando demandas urgentes.`
                });
              } else if (resolutionRate < 50) {
                insights.push({
                  icon: '⚡',
                  title: 'Oportunidade de Melhoria',
                  description: `Taxa de resolução de ${resolutionRate.toFixed(1)}% pode ser melhorada. Considere revisar processos internos.`
                });
              }
              
              if (avgEngagement > 20) {
                insights.push({
                  icon: '🚀',
                  title: 'Alto Engajamento',
                  description: `Média de ${Math.round(avgEngagement)} interações por post indica forte conexão com cidadãos.`
                });
              } else if (socialPosts?.length > 0 && avgEngagement < 10) {
                insights.push({
                  icon: '📈',
                  title: 'Potencial de Crescimento',
                  description: `Engajamento de ${Math.round(avgEngagement)} por post. Experimente horários diferentes ou conteúdo mais interativo.`
                });
              }
              
              const urgentRequests = requests?.filter(r => r.priority === 'alta').length || 0;
              if (urgentRequests === 0 && requests?.length > 5) {
                insights.push({
                  icon: '✅',
                  title: 'Gestão Preventiva',
                  description: 'Nenhuma demanda urgente! Sua gestão preventiva está funcionando bem.'
                });
              }
              
              const totalShares = socialPosts?.reduce((sum, post) => sum + (post.shares || 0), 0) || 0;
              const totalLikes = socialPosts?.reduce((sum, post) => sum + (post.likes || 0), 0) || 0;
              if (totalShares > totalLikes * 0.2) {
                insights.push({
                  icon: '🌟',
                  title: 'Conteúdo Viral',
                  description: `${totalShares} compartilhamentos indicam que seu conteúdo ressoa com a audiência.`
                });
              }
              
              if (insights.length === 0) {
                insights.push({
                  icon: '📊',
                  title: 'Análise em Desenvolvimento',
                  description: 'Continue adicionando dados para insights mais precisos sobre o sentimento dos cidadãos.'
                });
              }
              
              return insights.map((insight, index) => (
                <div key={index} className={`p-3 rounded-lg ${darkMode ? 'bg-gray-700/50' : 'bg-gray-50'}`}>
                  <div className="flex items-start space-x-3">
                    <span className="text-2xl">{insight.icon}</span>
                    <div>
                      <h4 className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                        {insight.title}
                      </h4>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {insight.description}
                      </p>
                    </div>
                  </div>
                </div>
              ));
            })()}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};