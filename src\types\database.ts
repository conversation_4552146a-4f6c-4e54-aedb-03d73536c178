export interface Database {
  public: {
    Tables: {
      citizens: {
        Row: {
          id: number
          name: string
          email: string
          phone: string
          cpf?: string
          birth_date?: string
          address: any
          social_media: any
          category: string
          tags: string[]
          notes?: string
          last_contact: string
          status: 'active' | 'inactive' | 'blocked'
          created_at: string
          updated_at: string
        }
        Insert: Omit<Database['public']['Tables']['citizens']['Row'], 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Database['public']['Tables']['citizens']['Insert']>
      }
      projects: {
        Row: {
          id: number
          name: string
          protocol: string
          category: string
          status: string
          budget: number
          description?: string
          created_at: string
          updated_at: string
        }
        Insert: Omit<Database['public']['Tables']['projects']['Row'], 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Database['public']['Tables']['projects']['Insert']>
      }
      commitments: {
        Row: {
          id: number
          title: string
          description?: string
          date: string
          time: string
          status: 'confirmado' | 'pendente' | 'cancelado'
          location?: string
          attendees: string[]
          project_id?: number
          project_name?: string
          demand_id?: number
          demand_title?: string
          created_at: string
          updated_at: string
        }
        Insert: Omit<Database['public']['Tables']['commitments']['Row'], 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Database['public']['Tables']['commitments']['Insert']>
      }
      demands: {
        Row: {
          id: number
          citizen_id?: number
          citizen_name: string
          subject: string
          description: string
          type: 'oficio' | 'indicacao' | 'requerimento' | 'emenda'
          priority: 'alta' | 'media' | 'baixa'
          status: 'protocolado' | 'em_andamento' | 'resolvido' | 'cancelado'
          responsible_staff?: string
          project_id?: number
          deadline?: string
          resolution_notes?: string
          created_at: string
          updated_at: string
        }
        Insert: Omit<Database['public']['Tables']['demands']['Row'], 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Database['public']['Tables']['demands']['Insert']>
      }
      social_posts: {
        Row: {
          id: number
          platform: 'Facebook' | 'Instagram' | 'Twitter' | 'LinkedIn'
          content: string
          likes: number
          comments: number
          shares: number
          scheduled_date?: string
          published_date?: string
          status: 'draft' | 'scheduled' | 'published' | 'failed'
          hashtags: string[]
          mentions: string[]
          media_urls: string[]
          created_at: string
          updated_at: string
        }
        Insert: Omit<Database['public']['Tables']['social_posts']['Row'], 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Database['public']['Tables']['social_posts']['Insert']>
      }
      team_members: {
        Row: {
          id: number
          name: string
          role: string
          area: string
          rating: number
          email: string
          phone: string
          tasks_today: number
          tasks_week: number
          created_at: string
          updated_at: string
        }
        Insert: Omit<Database['public']['Tables']['team_members']['Row'], 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Database['public']['Tables']['team_members']['Insert']>
      }
      office_settings: {
        Row: {
          id: number
          office_name: string
          office_type: string
          holder_name: string
          institution_name: string
          city: string
          state: string
          address?: string
          phone?: string
          email?: string
          website?: string
          logo_url?: string
          coat_of_arms_url?: string
          social_media: any
          created_at: string
          updated_at: string
        }
        Insert: Omit<Database['public']['Tables']['office_settings']['Row'], 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Database['public']['Tables']['office_settings']['Insert']>
      }
      users: {
        Row: {
          id: number
          email: string
          name: string
          role: 'titular' | 'assessor_principal' | 'assessor_marketing' | 'assessor'
          phone?: string
          avatar_url?: string
          permissions: any
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: Omit<Database['public']['Tables']['users']['Row'], 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Database['public']['Tables']['users']['Insert']>
      }
    }
  }
}