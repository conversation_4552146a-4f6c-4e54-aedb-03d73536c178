import { supabase } from '@/lib/supabase'

export interface OfficeSettings {
  id: number
  office_name: string
  office_type: string
  holder_name: string
  institution_name: string
  city: string
  state: string
  address?: string
  phone?: string
  email?: string
  website?: string
  logo_url?: string
  coat_of_arms_url?: string
  social_media: any
  created_at: string
  updated_at: string
}

export const officeSettingsService = {
  async get() {
    const { data, error } = await supabase
      .from('office_settings')
      .select('*')
      .single()
    
    if (error) throw error
    return data
  },

  async update(settings: Partial<OfficeSettings>) {
    const { data, error } = await supabase
      .from('office_settings')
      .update(settings)
      .eq('id', 1)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async uploadLogo(file: File) {
    const fileExt = file.name.split('.').pop()
    const fileName = `logo-${Date.now()}.${fileExt}`
    
    const { data, error } = await supabase.storage
      .from('office-assets')
      .upload(fileName, file)
    
    if (error) throw error
    
    const { data: { publicUrl } } = supabase.storage
      .from('office-assets')
      .getPublicUrl(fileName)
    
    return publicUrl
  },

  async uploadCoatOfArms(file: File) {
    const fileExt = file.name.split('.').pop()
    const fileName = `coat-of-arms-${Date.now()}.${fileExt}`
    
    const { data, error } = await supabase.storage
      .from('office-assets')
      .upload(fileName, file)
    
    if (error) throw error
    
    const { data: { publicUrl } } = supabase.storage
      .from('office-assets')
      .getPublicUrl(fileName)
    
    return publicUrl
  }
}