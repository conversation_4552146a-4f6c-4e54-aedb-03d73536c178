import { z } from 'zod';

// Esquemas de validação
export const aiRequestSchema = z.object({
  page: z.string().min(1).max(50),
  messages: z.array(z.object({
    role: z.enum(['user', 'assistant', 'system']),
    content: z.string().min(1).max(4000)
  })).min(1).max(10)
});

export const signupSchema = z.object({
  email: z.string().email('Email inválido'),
  password: z.string().min(8, 'Senha deve ter pelo menos 8 caracteres'),
  name: z.string().min(2, 'Nome deve ter pelo menos 2 caracteres').max(100),
  role: z.enum(['titular', 'assessor_principal', 'assessor_marketing', 'assessor'])
});

export const citizenSchema = z.object({
  name: z.string().min(2).max(100),
  email: z.string().email(),
  phone: z.string().regex(/^\d{10,11}$/, 'Telefone inválido'),
  cpf: z.string().regex(/^\d{11}$/, 'CPF inválido').optional(),
  category: z.string().min(1).max(50),
  notes: z.string().max(1000).optional()
});

// Função para sanitizar strings
export function sanitizeString(input: string): string {
  return input
    .trim()
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
    .replace(/[<>]/g, '') // Remove < e >
    .substring(0, 1000); // Limita tamanho
}

// Função para validar e sanitizar dados
export function validateAndSanitize<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; error: string } {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      return { 
        success: false, 
        error: `${firstError.path.join('.')}: ${firstError.message}` 
      };
    }
    return { success: false, error: 'Dados inválidos' };
  }
}