import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();
    
    console.log('Testando login com:', { email });
    
    // Usar o cliente com service role para testar
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    console.log('Resultado do login:', { data, error });
    
    if (error) {
      return NextResponse.json({ 
        success: false, 
        error: error.message,
        code: error.status
      }, { status: 400 });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Login realizado com sucesso',
      user: {
        id: data.user?.id,
        email: data.user?.email,
        confirmed: data.user?.email_confirmed_at ? true : false
      }
    });
    
  } catch (error) {
    console.error('Erro no teste de login:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Listar usuários para debug
    const { data: users, error } = await supabase.auth.admin.listUsers();
    
    if (error) {
      return NextResponse.json({ 
        success: false, 
        error: error.message 
      }, { status: 400 });
    }
    
    return NextResponse.json({ 
      success: true, 
      users: users.users.map(user => ({
        id: user.id,
        email: user.email,
        confirmed: user.email_confirmed_at ? true : false,
        created_at: user.created_at
      }))
    });
    
  } catch (error) {
    console.error('Erro ao listar usuários:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno' 
    }, { status: 500 });
  }
}
