"use client";
import { useState, useEffect } from 'react';
import { Plus, Star, Mail, Phone, Briefcase, CheckCircle } from 'lucide-react';
import { NewTeamMemberModal } from './NewTeamMemberModal';
import { teamService } from '@/services/teamService';

interface TeamMember {
 id: number;
 name: string;
 role: string;
 area: string;
 rating: number;
 email: string;
 phone: string;
 member: string;
 darkMode: boolean;
 tasksToday: number;
 tasksWeek: number;
}

const TeamMemberCard = ({ member, darkMode }: { member: TeamMember, darkMode: boolean }) => (
    <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white'} p-6 rounded-lg shadow-sm border text-center`}>
        <div className="w-24 h-24 rounded-full mx-auto mb-4 flex items-center justify-center bg-gray-200 dark:bg-gray-700">
            <span className={`text-4xl font-bold ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{member.name[0]}</span>
        </div>
        <h3 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>{member.name}</h3>
        <p className={`${darkMode ? 'text-blue-400' : 'text-blue-600'} font-semibold`}>{member.role}</p>
        <p className={`text-sm mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>{member.area}</p>
        
        <div className="flex justify-center items-center space-x-1 mt-2">
            {[...Array(5)].map((_, i) => (
                <Star key={i} className={`w-5 h-5 ${i < Math.round(member.rating) ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'}`} fill="currentColor" />
            ))}
            <span className={`text-sm font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>{member.rating.toFixed(1)}</span>
        </div>

        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 space-y-2 text-sm text-left">
            <div className="flex items-center space-x-2">
                <Mail className="w-4 h-4 text-gray-400" />
                <span className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>{member.email}</span>
            </div>
            <div className="flex items-center space-x-2">
                <Phone className="w-4 h-4 text-gray-400" />
                <span className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>{member.phone}</span>
            </div>
        </div>

        <div className="mt-4 flex justify-between items-center text-xs">
            <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Tarefas (hoje/semana)</span>
            <span className={`font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>{member.tasksToday} / {member.tasksWeek}</span>
        </div>
    </div>
);


export const Team = ({ darkMode }: { darkMode: boolean }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [officeTeam, setOfficeTeam] = useState<TeamMember[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        loadTeamMembers();
    }, []);

    const loadTeamMembers = async () => {
        try {
            const data = await teamService.getAll();
            setOfficeTeam(data || []);
        } catch (error) {
            console.error('Error loading team members:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <h1 className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Equipe do Gabinete</h1>
                <button onClick={() => setIsModalOpen(true)} className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
                    <Plus className="w-4 h-4" />
                    <span>Adicionar Membro</span>
                </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {officeTeam.map(member => (
                    <TeamMemberCard key={member.id} member={member} darkMode={darkMode} />
                ))}
            </div>
            <NewTeamMemberModal isOpen={isModalOpen} setIsOpen={setIsModalOpen} darkMode={darkMode} onSave={loadTeamMembers} />
        </div>
    )
};