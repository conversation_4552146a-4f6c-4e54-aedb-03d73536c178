"use client";
import { But<PERSON> } from "@/app/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Textarea } from "@/app/components/ui/textarea";

export const NewRequestModal = ({ isOpen, setIsOpen, darkMode, onSave }) => {
    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className={`${darkMode ? 'bg-gray-800 border-gray-700' : ''}`}>
                <DialogHeader>
                    <DialogTitle className={`${darkMode ? 'text-white' : ''}`}>Nova Demanda</DialogTitle>
                    <DialogDescription className={`${darkMode ? 'text-gray-400' : ''}`}>
                        Preencha os detalhes da nova demanda.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="citizenName" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                            Solicitante
                        </Label>
                        <Input id="citizenName" className="col-span-3" />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="subject" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                            Assunto
                        </Label>
                        <Input id="subject" className="col-span-3" />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="description" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                            Descrição
                        </Label>
                        <Textarea id="description" className="col-span-3" />
                    </div>
                </div>
                <DialogFooter>
                    <Button type="submit" onClick={() => { setIsOpen(false); onSave?.(); }}>Salvar</Button>
                    <Button variant="outline" onClick={() => setIsOpen(false)}>Cancelar</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};
