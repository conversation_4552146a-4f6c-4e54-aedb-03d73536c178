"use client";
import { But<PERSON> } from "@/app/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";

interface NewTeamMemberModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  darkMode: boolean;
}

export const NewTeamMemberModal = ({ isOpen, setIsOpen, darkMode }: NewTeamMemberModalProps) => {
    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className={`${darkMode ? 'bg-gray-800 border-gray-700' : ''}`}>
                <DialogHeader>
                    <DialogTitle className={`${darkMode ? 'text-white' : ''}`}>Adicionar Membro da Equipe</DialogTitle>
                    <DialogDescription className={`${darkMode ? 'text-gray-400' : ''}`}>
                        Preencha os detalhes do novo membro.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="name" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                            Nome
                        </Label>
                        <Input id="name" className={`col-span-3 ${darkMode ? 'text-gray-100' : ''}`} />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="role" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                            Cargo
                        </Label>
                        <Input id="role" className={`col-span-3 ${darkMode ? 'text-gray-100' : ''}`} />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="email" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                            Email
                        </Label>
                        <Input id="email" type="email" className={`col-span-3 ${darkMode ? 'text-gray-100' : ''}`} />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="phone" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                            Telefone
                        </Label>
                        <Input id="phone" className={`col-span-3 ${darkMode ? 'text-gray-100' : ''}`} />
                    </div>
                </div>
                <DialogFooter>
                    <Button type="submit" onClick={() => setIsOpen(false)}>Salvar</Button>
                    <Button variant="outline" onClick={() => setIsOpen(false)}>Cancelar</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};
