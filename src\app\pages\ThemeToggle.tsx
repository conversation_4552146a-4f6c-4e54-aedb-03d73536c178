"use client";
import { Sun, Moon } from 'lucide-react';

export const ThemeToggle = ({ darkMode, setDarkMode }: { darkMode: boolean, setDarkMode: (value: boolean) => void }) => {
  return (
    <button onClick={() => setDarkMode(!darkMode)} className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400">
      {darkMode ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
    </button>
  );
};
