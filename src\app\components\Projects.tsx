"use client";
import { useState } from 'react';
import { Plus, MoreHorizontal, Eye, Edit, Trash2 } from 'lucide-react';
import {
  Table, TableHeader, TableRow, TableHead, TableBody, TableCell,
} from "@/app/components/ui/table";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { NewProjectModal } from './NewProjectModal';

interface Project {
  id: number;
  name: string;
  protocol: string;
  category: string;
  status: string;
  budget: number;
}

export const Projects = ({ projects, getStatusColor, darkMode }: { projects: Project[], getStatusColor: (status: string) => string, darkMode: boolean }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(value);
    }

    return (
        <div>
            <div className="flex justify-between items-center mb-6">
                <button onClick={() => setIsModalOpen(true)} className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2">
                    <Plus className="w-4 h-4" />
                    <span>Novo Projeto</span>
                </button>
            </div>
            <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                <Table>
                    <TableHeader>
                        <TableRow className={`${darkMode ? 'border-gray-700' : ''}`}>
                            <TableHead className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Nome</TableHead>
                            <TableHead className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Protocolo</TableHead>
                            <TableHead className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Categoria</TableHead>
                            <TableHead className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Status</TableHead>
                            <TableHead className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Orçamento</TableHead>
                            <TableHead className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Ações</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {projects.map((project) => (
                            <TableRow key={project.id} className={`${darkMode ? 'border-gray-700' : ''}`}>
                                <TableCell className={`font-medium ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{project.name}</TableCell>
                                <TableCell className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{project.protocol}</TableCell>
                                <TableCell className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{project.category}</TableCell>
                                <TableCell>
                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                                        {project.status.replace('_', ' ')}
                                    </span>
                                </TableCell>
                                <TableCell className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{formatCurrency(project.budget)}</TableCell>
                                <TableCell>
                                    <DropdownMenu.Root>
                                        <DropdownMenu.Trigger asChild>
                                            <button className={`p-2 rounded-full ${darkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-200 text-gray-600'}`}>
                                                <MoreHorizontal className="w-4 h-4" />
                                            </button>
                                        </DropdownMenu.Trigger>
                                        <DropdownMenu.Content className={`${darkMode ? 'bg-gray-900 border-gray-700' : 'bg-white'}`}>
                                            <DropdownMenu.Item className={`${darkMode ? 'text-gray-300 hover:!bg-gray-700' : 'text-gray-700'}`}><Eye className="w-4 h-4 mr-2" />Ver Detalhes</DropdownMenu.Item>
                                            <DropdownMenu.Item className={`${darkMode ? 'text-gray-300 hover:!bg-gray-700' : 'text-gray-700'}`}><Edit className="w-4 h-4 mr-2" />Editar</DropdownMenu.Item>
                                            <DropdownMenu.Item className={`${darkMode ? 'text-red-400 hover:!bg-gray-700' : 'text-red-600'}`}><Trash2 className="w-4 h-4 mr-2" />Excluir</DropdownMenu.Item>
                                        </DropdownMenu.Content>
                                    </DropdownMenu.Root>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
            <NewProjectModal isOpen={isModalOpen} setIsOpen={setIsModalOpen} darkMode={darkMode} />
        </div>
    )
};