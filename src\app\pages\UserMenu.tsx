"use client";
import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { User, Settings, LogOut } from 'lucide-react';

export const UserMenu = ({ user, darkMode, onProfileClick, onSettingsClick }: { 
  user: any, 
  darkMode: boolean,
  onProfileClick: () => void,
  onSettingsClick: () => void
}) => {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const { signOut } = useAuth();

  const handleLogout = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  return (
    <div className="relative user-menu-dropdown">
      <button onClick={() => setShowUserMenu(!showUserMenu)} className="flex items-center space-x-2">
        {user?.user_metadata?.avatar_url ? (
          <img src={user.user_metadata.avatar_url} alt="Avatar" className="w-8 h-8 rounded-full object-cover" />
        ) : (
          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
            {user?.user_metadata?.name?.charAt(0) || user?.email?.charAt(0) || 'U'}
          </div>
        )}
        <div className="hidden md:block text-left">
          <p className={`text-sm font-medium ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
            {user?.user_metadata?.name || user?.email}
          </p>
          <p className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-600'}`}>
            {user?.user_metadata?.role || 'Usuário'}
          </p>
        </div>
      </button>
      {showUserMenu && (
        <div className={`absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border`}>
          <button 
            onClick={() => {
              onProfileClick();
              setShowUserMenu(false);
            }}
            className={`flex items-center space-x-2 w-full px-4 py-2 text-sm ${darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100'}`}
          >
            <User className="w-4 h-4" />
            <span>Meu Perfil</span>
          </button>
          <button 
            onClick={() => {
              onSettingsClick();
              setShowUserMenu(false);
            }}
            className={`flex items-center space-x-2 w-full px-4 py-2 text-sm ${darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100'}`}
          >
            <Settings className="w-4 h-4" />
            <span>Configurações</span>
          </button>
          <div className={`border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'} my-1`}></div>
          <button 
            onClick={handleLogout}
            className={`flex items-center space-x-2 w-full px-4 py-2 text-sm ${darkMode ? 'text-red-400 hover:bg-gray-700' : 'text-red-600 hover:bg-gray-100'}`}
          >
            <LogOut className="w-4 h-4" />
            <span>Sair</span>
          </button>
        </div>
      )}
    </div>
  );
};
