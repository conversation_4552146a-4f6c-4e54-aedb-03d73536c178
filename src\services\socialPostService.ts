import { supabase } from '@/lib/supabase'
import { Database } from '@/types/database'

type SocialPost = Database['public']['Tables']['social_posts']['Row']
type SocialPostInsert = Database['public']['Tables']['social_posts']['Insert']
type SocialPostUpdate = Database['public']['Tables']['social_posts']['Update']

export const socialPostService = {
  async getAll() {
    const { data, error } = await supabase
      .from('social_posts')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  async getPublished() {
    const { data, error } = await supabase
      .from('social_posts')
      .select('*')
      .eq('status', 'published')
      .order('published_date', { ascending: false })
    
    if (error) throw error
    return data
  },

  async create(post: SocialPostInsert) {
    const { data, error } = await supabase
      .from('social_posts')
      .insert(post)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async update(id: number, updates: SocialPostUpdate) {
    const { data, error } = await supabase
      .from('social_posts')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async delete(id: number) {
    const { error } = await supabase
      .from('social_posts')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}