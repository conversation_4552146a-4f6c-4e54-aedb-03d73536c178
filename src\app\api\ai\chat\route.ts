import { NextRequest, NextResponse } from 'next/server';

// Rota de compatibilidade - redireciona para a nova API multi-tenant
export async function POST(request: NextRequest) {
  try {
    const { messages } = await request.json();
    
    // Redirecionar para a nova API multi-tenant
    const response = await fetch(`${request.nextUrl.origin}/api/ai`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Nota: Esta rota de compatibilidade não tem autenticação própria
      },
      body: JSON.stringify({
        page: 'dashboard',
        messages: messages
      })
    });

    if (response.ok) {
      const data = await response.json();
      return NextResponse.json({ response: data.response });
    }

    // Fallback simples se a nova API falhar
    return NextResponse.json({ 
      response: 'Assistente temporariamente indisponível. Tente novamente.' 
    });

  } catch (error) {
    console.error('Erro na rota de compatibilidade:', error);
    return NextResponse.json({ 
      response: '<PERSON><PERSON><PERSON><PERSON>, não consegui processar sua solicitação no momento.' 
    });
  }
}