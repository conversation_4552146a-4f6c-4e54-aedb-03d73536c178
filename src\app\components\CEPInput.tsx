import { useState } from 'react';
import { Search } from 'lucide-react';
import { cepService } from '@/services/cepService';

interface CEPInputProps {
  onAddressFound?: (address: {
    street: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
  }) => void;
  darkMode?: boolean;
  className?: string;
}

export const CEPInput = ({ onAddressFound, darkMode = false, className = '' }: CEPInputProps) => {
  const [cep, setCep] = useState('');
  const [loading, setLoading] = useState(false);

  const handleCEPSearch = async () => {
    if (!cep) return;
    
    setLoading(true);
    try {
      const cepData = await cepService.buscarCEP(cep);
      if (cepData) {
        const address = {
          street: cepData.logradouro,
          neighborhood: cepData.bairro,
          city: cepData.localidade,
          state: cepData.uf,
          zipCode: cepService.formatCEP(cepData.cep)
        };
        
        setCep(address.zipCode);
        onAddressFound?.(address);
      } else {
        alert('CEP não encontrado');
      }
    } catch (error) {
      console.error('Erro ao buscar CEP:', error);
      alert('Erro ao buscar CEP');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`flex gap-2 ${className}`}>
      <input
        type="text"
        value={cep}
        onChange={(e) => setCep(e.target.value)}
        placeholder="00000-000"
        className={`flex-1 px-3 py-2 border rounded-md ${
          darkMode 
            ? 'bg-gray-700 border-gray-600 text-gray-100' 
            : 'bg-white border-gray-300 text-gray-900'
        }`}
        onKeyPress={(e) => e.key === 'Enter' && handleCEPSearch()}
      />
      <button
        type="button"
        onClick={handleCEPSearch}
        disabled={loading || !cep}
        className={`px-3 py-2 rounded-md ${
          darkMode 
            ? 'bg-blue-600 hover:bg-blue-700 text-white' 
            : 'bg-blue-600 hover:bg-blue-700 text-white'
        } disabled:opacity-50`}
      >
        {loading ? '...' : <Search className="w-4 h-4" />}
      </button>
    </div>
  );
};