"use client";
import { MapPin, Phone, Calendar as CalendarIcon, User, Mail, Facebook, Instagram, Twitter, Linkedin, MessageCircle, Tag } from 'lucide-react';

interface Citizen {
  id: number;
  name: string;
  email: string;
  phone: string;
  cpf: string;
  birthDate: string;
  address: {
    street: string;
    number: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
  };
  socialMedia: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
    whatsapp?: string;
  };
  category: string;
  tags: string[];
  notes: string;
  lastContact: string;
  status: 'active' | 'inactive' | 'blocked';
}

export const CitizenCard = ({ citizen, onClick, darkMode, getStatusColor }: { citizen: Citizen, onClick: (citizen: Citizen) => void, darkMode: boolean, getStatusColor: (status: string) => string }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getSocialIcons = () => {
    const icons = [];
    if (citizen.socialMedia?.facebook) icons.push(<Facebook key="fb" className="w-3 h-3" />);
    if (citizen.socialMedia?.instagram) icons.push(<Instagram key="ig" className="w-3 h-3" />);
    if (citizen.socialMedia?.twitter) icons.push(<Twitter key="tw" className="w-3 h-3" />);
    if (citizen.socialMedia?.linkedin) icons.push(<Linkedin key="li" className="w-3 h-3" />);
    if (citizen.socialMedia?.whatsapp) icons.push(<MessageCircle key="wa" className="w-3 h-3" />);
    return icons;
  };

  return (
    <div 
      className={`${darkMode ? 'bg-gray-800 border-gray-700 hover:bg-gray-750' : 'bg-white border-gray-200 hover:shadow-md'} p-4 rounded-lg shadow-sm border transition-all cursor-pointer`}
      onClick={() => onClick(citizen)}
    >
        <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
                <h3 className={`font-medium ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{citizen.name}</h3>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{citizen.category}</p>
            </div>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(citizen.status)}`}>
                {citizen.status}
            </span>
        </div>
        
        <div className={`space-y-2 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            <div className="flex items-center space-x-2">
                <Mail className="w-4 h-4" />
                <span className="truncate">{citizen.email}</span>
            </div>
            <div className="flex items-center space-x-2">
                <Phone className="w-4 h-4" />
                <span>{citizen.phone}</span>
            </div>
            <div className="flex items-center space-x-2">
                <MapPin className="w-4 h-4" />
                <span className="truncate">{citizen.address?.neighborhood || 'N/A'}, {citizen.address?.city || 'N/A'}</span>
            </div>
            <div className="flex items-center space-x-2">
                <CalendarIcon className="w-4 h-4" />
                <span>Último: {formatDate(citizen.lastContact)}</span>
            </div>
        </div>

        {citizen.tags && citizen.tags.length > 0 && (
            <div className="mt-3 flex flex-wrap gap-1">
                {citizen.tags.slice(0, 3).map((tag, index) => (
                    <span key={index} className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'}`}>
                        {tag}
                    </span>
                ))}
                {citizen.tags.length > 3 && (
                    <span className={`px-2 py-1 rounded text-xs ${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'}`}>
                        +{citizen.tags.length - 3}
                    </span>
                )}
            </div>
        )}

        {getSocialIcons().length > 0 && (
            <div className="mt-3 flex items-center space-x-2">
                <span className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>Redes:</span>
                <div className="flex space-x-1">
                    {getSocialIcons()}
                </div>
            </div>
        )}
    </div>
  );
};
