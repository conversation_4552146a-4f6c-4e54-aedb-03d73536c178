import React, { useState, useEffect } from 'react';
// Ícones relevantes para o contexto político foram mantidos/adicionados
import {
  Landmark, Users, MessageSquare, FileText, BarChart3, Settings, Plus, Search, Bell, Menu, X, ChevronRight, MapPin, Phone, Mail, Clock, CheckCircle, AlertCircle, User, Target, Briefcase, Filter, Download, Share2, Eye, Edit, Trash2, Star, Heart, MessageCircle, Send, UserPlus, Shield, Key, Camera, Video, Mic, Link, Hash, TrendingUp, PieChart, Activity, DollarSign, UserCheck, Calendar as CalendarIcon, Clock as ClockIcon, CheckSquare, Archive, Flag, Zap, Globe, Facebook, Instagram, Twitter, Linkedin, Youtube, Bookmark, Tag, Award, BadgeCheck, AlertTriangle, Info, CheckCircle2, XCircle, Clock4, MoreHorizontal, ArrowRight, ArrowLeft, ChevronDown, ChevronUp, RefreshCw, Upload, Download as DownloadIcon, Megaphone, FileSignature, G<PERSON><PERSON>, Vote, Handshake
} from 'lucide-react';

const PoliticalOfficeApp = () => {
  // --- STATE MANAGEMENT ---
  const [currentView, setCurrentView] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [selectedCitizen, setSelectedCitizen] = useState(null);
  const [selectedCommitment, setSelectedCommitment] = useState(null);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  
  // Dados do usuário (ex: Chefe de Gabinete)
  const [user, setUser] = useState({
    name: 'Carlos Andrade',
    role: 'Chefe de Gabinete',
    office: 'Gabinete Dep. Federal Joana Silva',
    avatar: 'keys/avatar-political-advisor?prompt=professional%20male%20political%20advisor%20suit',
    permissions: ['admin', 'manage_citizens', 'view_analytics', 'manage_agenda']
  });

  const [darkMode, setDarkMode] = useState(false);

  // --- DADOS SIMULADOS PARA GABINETE POLÍTICO ---

  // Notificações
  const [notifications, setNotifications] = useState([
    { id: 1, type: 'urgent', title: 'Votação Urgente na Câmara', description: 'PL 123/2025 - Orçamento da União', time: 'em 30 minutos', read: false, color: 'red' },
    { id: 2, type: 'warning', title: 'Demanda de Liderança', description: 'João da Saúde solicita reunião sobre hospital regional', time: 'há 1 hora', read: false, color: 'yellow' },
    { id: 3, type: 'reminder', title: 'Entrevista Agendada', description: 'Entrevista para a Rádio CBN', time: 'Amanhã às 09:00', read: false, color: 'blue' },
    { id: 4, type: 'success', title: 'Emenda Aprovada', description: 'Emenda para a educação de jovens foi aprovada', time: 'há 3 horas', read: true, color: 'green' }
  ]);
  const unreadNotifications = notifications.filter(n => !n.read).length;

  // Cidadãos e Contatos
  const [citizens, setCitizens] = useState([
    { id: 1, name: 'Maria Oliveira', email: '<EMAIL>', phone: '(61) 99999-1111', region: 'Asa Sul, Brasília', demandArea: 'Saúde', leader: 'João da Saúde', lastContact: '2025-08-15', status: 'apoiador', notes: 'Solicitou melhorias no posto de saúde local.' },
    { id: 2, name: 'José Pereira', email: '<EMAIL>', phone: '(61) 98888-2222', region: 'Taguatinga, Brasília', demandArea: 'Educação', leader: 'Ana dos Professores', lastContact: '2025-08-10', status: 'neutro', notes: 'Preocupado com a falta de vagas em creches.' },
    { id: 3, name: 'Antônio Costa', email: '<EMAIL>', phone: '(61) 97777-3333', region: 'Guará, Brasília', demandArea: 'Segurança', leader: 'Sargento Lima', lastContact: '2025-08-20', status: 'apoiador', notes: 'Líder comunitário, pede mais policiamento no bairro.' }
  ]);

  // Agenda de Compromissos
  const [commitments, setCommitments] = useState([
    { id: 1, title: 'Reunião com Sindicato dos Professores', responsible: 'Carlos Andrade', date: '2025-09-02', time: '10:00', duration: 60, type: 'Reunião', location: 'Gabinete', status: 'agendado', topic: 'Piso salarial e plano de carreira.' },
    { id: 2, title: 'Sessão Plenária - Votação Orçamento', responsible: 'Dep. Joana Silva', date: '2025-09-02', time: '14:00', duration: 180, type: 'Evento Oficial', location: 'Plenário da Câmara', status: 'confirmado', topic: 'Votação do Orçamento da União para 2026.' },
    { id: 3, title: 'Visita à Comunidade do Sol Nascente', responsible: 'Mariana Lima', date: '2025-09-03', time: '09:30', duration: 120, type: 'Visita', location: 'Sol Nascente, Ceilândia', status: 'realizado', topic: 'Ouvir demandas da população sobre saneamento básico.' }
  ]);

  // Equipe do Gabinete
  const [officeTeam, setOfficeTeam] = useState([
    { id: 1, name: 'Mariana Lima', role: 'Assessora de Articulação Política', email: '<EMAIL>', phone: '(61) 99999-0001', area: 'Articulação Política', status: 'ativo', tasksToday: 5, tasksWeek: 25, rating: 4.9 },
    { id: 2, name: 'Pedro Alves', role: 'Assessor de Imprensa', email: '<EMAIL>', phone: '(61) 99999-0002', area: 'Comunicação', status: 'ativo', tasksToday: 8, tasksWeek: 35, rating: 4.8 },
    { id: 3, name: 'Sofia Ribeiro', role: 'Assessora Jurídica', email: '<EMAIL>', phone: '(61) 99999-0003', area: 'Jurídico', status: 'ativo', tasksToday: 3, tasksWeek: 15, rating: 4.9 }
  ]);

  // Demandas e Solicitações dos Cidadãos
  const [requests, setRequests] = useState([
    { id: 1, citizenName: 'Maria Oliveira', date: '2025-08-15', type: 'Ofício', responsibleStaff: 'Mariana Lima', subject: 'Reforma do Posto de Saúde', description: 'Solicita envio de ofício para a Secretaria de Saúde pedindo reforma.', status: 'em_andamento', priority: 'alta' },
    { id: 2, citizenName: 'José Pereira', date: '2025-08-10', type: 'Indicação', responsibleStaff: 'Carlos Andrade', subject: 'Construção de Creche', description: 'Indicação legislativa para construção de nova creche em Taguatinga.', status: 'resolvido', priority: 'media' },
    { id: 3, citizenName: 'Antônio Costa', date: '2025-08-20', type: 'Requerimento', responsibleStaff: 'Sofia Ribeiro', subject: 'Dados de Segurança Pública', description: 'Requerimento de informações sobre o efetivo policial no Guará.', status: 'protocolado', priority: 'baixa' }
  ]);

  // Projetos e Iniciativas (ex: Projetos de Lei)
  const [projects, setProjects] = useState([
    { id: 1, name: 'PL 245/2025 - Incentivo ao Primeiro Emprego', protocol: 'PL-245/2025', category: 'Projeto de Lei', status: 'em_tramitacao', budget: 0, description: 'Cria incentivos fiscais para empresas que contratarem jovens.' },
    { id: 2, name: 'Emenda 42 - Orçamento da Saúde', protocol: 'EMD-42/2025', category: 'Emenda', status: 'aprovado', budget: 5000000, description: 'Destina R$ 5 milhões para hospitais regionais.' },
    { id: 3, name: 'Indicação 112 - Ciclovia na EPTG', protocol: 'IND-112/2025', category: 'Indicação', status: 'enviado', budget: 0, description: 'Sugere ao poder executivo a construção de ciclovia na EPTG.' }
  ]);

  // Dados para Dashboard
  const [analytics, setAnalytics] = useState({
    totalCitizens: 1250,
    newRequests: 45,
    resolvedRequests: 18,
    commitmentsToday: 2,
    commitmentsWeek: 15,
    socialMediaEngagement: 12.5, // em %
    positiveMentions: 85, // em %
    monthlyGrowth: {
      citizens: 5,
      requests: 15,
      engagement: 2.1
    },
    requestsByArea: {
      'Saúde': 45,
      'Educação': 32,
      'Segurança': 28,
      'Infraestrutura': 25,
      'Social': 20
    }
  });

  // --- EFEITOS E FUNÇÕES AUXILIARES ---

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showNotifications && !event.target.closest('.notifications-dropdown')) setShowNotifications(false);
      if (showUserMenu && !event.target.closest('.user-menu-dropdown')) setShowUserMenu(false);
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showNotifications, showUserMenu]);

  const markNotificationAsRead = (id) => setNotifications(prev => prev.map(n => n.id === id ? { ...n, read: true } : n));
  const markAllAsRead = () => setNotifications(prev => prev.map(n => ({ ...n, read: true })));

  // Menu do Sidebar
  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { id: 'agenda', label: 'Agenda', icon: CalendarIcon },
    { id: 'citizens', label: 'Cidadãos', icon: Users },
    { id: 'requests', label: 'Demandas', icon: FileSignature },
    { id: 'projects', label: 'Projetos', icon: Gavel },
    { id: 'communications', label: 'Comunicação', icon: Megaphone },
    { id: 'analytics', label: 'Relatórios', icon: TrendingUp },
    { id: 'team', label: 'Equipe', icon: UserCheck },
    { id: 'settings', label: 'Configurações', icon: Settings }
  ];

  // Funções de Estilo
  const getPriorityColor = (priority) => {
    const styles = {
      alta: darkMode ? 'text-red-400 bg-red-900/30' : 'text-red-600 bg-red-50',
      media: darkMode ? 'text-yellow-400 bg-yellow-900/30' : 'text-yellow-600 bg-yellow-50',
      baixa: darkMode ? 'text-green-400 bg-green-900/30' : 'text-green-600 bg-green-50',
    };
    return styles[priority] || (darkMode ? 'text-gray-400 bg-gray-700' : 'text-gray-600 bg-gray-50');
  };

  const getStatusColor = (status) => {
    const styles = {
        agendado: darkMode ? 'text-blue-400 bg-blue-900/30' : 'text-blue-600 bg-blue-50',
        confirmado: darkMode ? 'text-green-400 bg-green-900/30' : 'text-green-600 bg-green-50',
        realizado: darkMode ? 'text-gray-400 bg-gray-700' : 'text-gray-600 bg-gray-50',
        cancelado: darkMode ? 'text-red-400 bg-red-900/30' : 'text-red-600 bg-red-50',
        em_andamento: darkMode ? 'text-orange-400 bg-orange-900/30' : 'text-orange-600 bg-orange-50',
        resolvido: darkMode ? 'text-green-400 bg-green-900/30' : 'text-green-600 bg-green-50',
        protocolado: darkMode ? 'text-purple-400 bg-purple-900/30' : 'text-purple-600 bg-purple-50',
        em_tramitacao: darkMode ? 'text-yellow-400 bg-yellow-900/30' : 'text-yellow-600 bg-yellow-50',
        aprovado: darkMode ? 'text-green-400 bg-green-900/30' : 'text-green-600 bg-green-50',
        enviado: darkMode ? 'text-blue-400 bg-blue-900/30' : 'text-blue-600 bg-blue-50',
    };
    return styles[status] || (darkMode ? 'text-gray-400 bg-gray-700' : 'text-gray-600 bg-gray-50');
};


  // --- COMPONENTES REUTILIZÁVEIS ---

  const StatCard = ({ title, value, change, icon: Icon, color = 'blue', prefix = '', suffix = '' }) => (
    <div className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} p-6 rounded-lg shadow-sm border`}>
      <div className="flex items-center justify-between">
        <div>
          <p className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{title}</p>
          <p className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{prefix}{value}{suffix}</p>
          {change !== undefined && (
            <p className={`text-sm ${change > 0 ? (darkMode ? 'text-green-400' : 'text-green-600') : (darkMode ? 'text-red-400' : 'text-red-600')}`}>
              {change > 0 ? '+' : ''}{change}% este mês
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full bg-${color}-100 dark:bg-${color}-900/30`}>
          <Icon className={`w-6 h-6 text-${color}-600 dark:text-${color}-400`} />
        </div>
      </div>
    </div>
  );

  const CitizenCard = ({ citizen, onClick }) => (
    <div 
      className={`${darkMode ? 'bg-gray-800 border-gray-700 hover:bg-gray-750' : 'bg-white border-gray-200 hover:shadow-md'} p-4 rounded-lg shadow-sm border transition-all cursor-pointer`}
      onClick={() => onClick(citizen)}
    >
        <div className="flex items-start justify-between mb-3">
            <div>
                <h3 className={`font-medium ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{citizen.name}</h3>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Principal demanda: {citizen.demandArea}</p>
            </div>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                citizen.status === 'apoiador' 
                ? (darkMode ? 'bg-green-900/30 text-green-400' : 'bg-green-100 text-green-700')
                : (darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-700')
            }`}>
                {citizen.status}
            </span>
        </div>
        <div className={`space-y-1 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            <div className="flex items-center space-x-2"><MapPin className="w-4 h-4" /><span>{citizen.region}</span></div>
            <div className="flex items-center space-x-2"><Phone className="w-4 h-4" /><span>{citizen.phone}</span></div>
            <div className="flex items-center space-x-2"><CalendarIcon className="w-4 h-4" /><span>Último contato: {citizen.lastContact}</span></div>
            <div className="flex items-center space-x-2"><User className="w-4 h-4" /><span>Liderança: {citizen.leader}</span></div>
        </div>
    </div>
  );

  const CommitmentCard = ({ commitment, onClick }) => (
    <div 
      className={`${darkMode ? 'bg-gray-800 border-gray-700 hover:bg-gray-750' : 'bg-white border-gray-200 hover:shadow-md'} p-4 rounded-lg shadow-sm border transition-all cursor-pointer`}
      onClick={() => onClick(commitment)}
    >
      <div className="flex items-start justify-between mb-3">
        <div>
            <h3 className={`font-medium ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{commitment.title}</h3>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{commitment.type} - {commitment.location}</p>
        </div>
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(commitment.status)}`}>
          {commitment.status}
        </span>
      </div>
      <div className={`space-y-1 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
        <div className="flex items-center space-x-2"><CalendarIcon className="w-4 h-4" /><span>{commitment.date} às {commitment.time}</span></div>
        <div className="flex items-center space-x-2"><Clock className="w-4 h-4" /><span>{commitment.duration} minutos</span></div>
        <div className="flex items-center space-x-2"><UserCheck className="w-4 h-4" /><span>Responsável: {commitment.responsible}</span></div>
      </div>
      {commitment.topic && (
        <div className={`mt-3 p-2 rounded ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
          <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}><strong>Pauta:</strong> {commitment.topic}</p>
        </div>
      )}
    </div>
  );

  // --- RENDERIZAÇÃO DAS VIEWS ---

  const renderDashboard = () => (
    <div className="space-y-6">
      <h1 className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Dashboard do Gabinete</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard title="Novas Demandas (Mês)" value={analytics.newRequests} change={analytics.monthlyGrowth.requests} icon={FileSignature} color="blue"/>
        <StatCard title="Compromissos Hoje" value={analytics.commitmentsToday} icon={CalendarIcon} color="green"/>
        <StatCard title="Engajamento Social" value={analytics.socialMediaEngagement.toFixed(1)} change={analytics.monthlyGrowth.engagement} icon={Megaphone} color="purple" suffix="%"/>
        <StatCard title="Menções Positivas" value={analytics.positiveMentions} icon={Heart} color="red" suffix="%"/>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
          <h2 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>Agenda de Hoje</h2>
          <div className="space-y-3">
            {commitments.filter(c => c.date === '2025-09-02').map(commitment => (
              <div key={commitment.id} className={`flex items-center justify-between p-3 ${darkMode ? 'bg-gray-700' : 'bg-gray-50'} rounded-lg`}>
                <div>
                  <h4 className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{commitment.title}</h4>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{commitment.time} - {commitment.location}</p>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(commitment.status)}`}>
                    {commitment.status}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
          <h2 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>Demandas por Área</h2>
          <div className="space-y-3">
          {Object.entries(analytics.requestsByArea).map(([area, count]) => (
              <div key={area} className="flex items-center justify-between">
                <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{area}</span>
                <div className="flex items-center space-x-2">
                  <div className={`w-20 ${darkMode ? 'bg-gray-700' : 'bg-gray-200'} rounded-full h-2`}>
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${(count / 50) * 100}%` }}></div>
                  </div>
                  <span className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-900'}`}>{count}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderAgenda = () => (
    <div className="space-y-6">
        <div className="flex items-center justify-between">
            <h1 className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Agenda de Compromissos</h1>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
                <Plus className="w-4 h-4" />
                <span>Novo Compromisso</span>
            </button>
        </div>
        <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
            {/* Filtros aqui */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {commitments.map(c => <CommitmentCard key={c.id} commitment={c} onClick={setSelectedCommitment} />)}
            </div>
        </div>
        {/* Modal de Detalhes do Compromisso aqui */}
    </div>
  );

  const renderCitizens = () => (
    <div className="space-y-6">
        <div className="flex items-center justify-between">
            <h1 className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Cidadãos e Contatos</h1>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
                <UserPlus className="w-4 h-4" />
                <span>Novo Contato</span>
            </button>
        </div>
        <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
            {/* Filtros aqui */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {citizens.map(c => <CitizenCard key={c.id} citizen={c} onClick={setSelectedCitizen} />)}
            </div>
        </div>
        {/* Modal de Detalhes do Cidadão aqui */}
    </div>
  );

  const renderRequests = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Demandas e Solicitações</h1>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
          <Plus className="w-4 h-4" />
          <span>Nova Demanda</span>
        </button>
      </div>
      <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
        {/* Filtros aqui */}
        <div className="space-y-4">
          {requests.map(req => (
            <div key={req.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h3 className={`font-medium ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>{req.subject}</h3>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Solicitante: {req.citizenName} | Responsável: {req.responsibleStaff}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(req.priority)}`}>{req.priority}</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(req.status)}`}>{req.status}</span>
                </div>
              </div>
              <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>{req.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
  
  // As outras funções de renderização (renderProjects, renderCommunications, etc.) seguiriam um padrão similar.
  // Por brevidade, vou focar nas principais e deixar as outras como placeholders.
  const renderProjects = () => <div className={`text-xl ${darkMode ? 'text-white' : 'text-black'}`}>Página de Projetos e Iniciativas</div>;
  const renderCommunications = () => <div className={`text-xl ${darkMode ? 'text-white' : 'text-black'}`}>Página de Comunicação e Mídia</div>;
  const renderAnalytics = () => <div className={`text-xl ${darkMode ? 'text-white' : 'text-black'}`}>Página de Relatórios</div>;
  const renderTeam = () => <div className={`text-xl ${darkMode ? 'text-white' : 'text-black'}`}>Página da Equipe</div>;
  const renderSettings = () => <div className={`text-xl ${darkMode ? 'text-white' : 'text-black'}`}>Página de Configurações</div>;


  // Seletor de conteúdo principal
  const renderContent = () => {
    switch(currentView) {
      case 'dashboard': return renderDashboard();
      case 'agenda': return renderAgenda();
      case 'citizens': return renderCitizens();
      case 'requests': return renderRequests();
      case 'projects': return renderProjects();
      case 'communications': return renderCommunications();
      case 'analytics': return renderAnalytics();
      case 'team': return renderTeam();
      case 'settings': return renderSettings();
      // Adicionar outras views aqui
      default: return renderDashboard();
    }
  };

  // --- JSX PRINCIPAL DO COMPONENTE ---
  return (
    <div className={`min-h-screen transition-colors ${darkMode ? 'bg-gray-900' : 'bg-gray-100'}`}>
      {/* Header */}
      <header className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} shadow-sm border-b`}>
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <button onClick={() => setSidebarOpen(!sidebarOpen)} className="md:hidden p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200">
                <Menu className="w-6 h-6" />
              </button>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <Landmark className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className={`text-xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Gabinete Digital</h1>
                  <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{user.office}</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              {/* Barra de Busca, Botão Dark Mode, Notificações e Menu do Usuário (código original pode ser mantido aqui) */}
              {/* ... (O código do header do app original pode ser colado aqui) ... */}
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className={`fixed inset-y-0 left-0 z-50 w-64 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg transform transition-all duration-300 ease-in-out ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} md:translate-x-0 md:static md:inset-0 md:z-0`}>
          <nav className="flex-1 p-4 space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => {
                    setCurrentView(item.id);
                    setSidebarOpen(false);
                  }}
                  className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${currentView === item.id 
                    ? (darkMode ? 'bg-blue-900/30 text-blue-400' : 'bg-blue-100 text-blue-700')
                    : (darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100')
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{item.label}</span>
                </button>
              );
            })}
          </nav>
        </aside>

        {/* Conteúdo Principal */}
        <main className="flex-1 md:ml-0">
          <div className="p-6 max-w-7xl mx-auto">
            {renderContent()}
          </div>
        </main>
      </div>

      {sidebarOpen && <div className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden" onClick={() => setSidebarOpen(false)} />}
    </div>
  );
};

export default PoliticalOfficeApp;