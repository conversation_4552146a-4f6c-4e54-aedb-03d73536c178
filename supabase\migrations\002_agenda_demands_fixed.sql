-- Create commitments table (agenda) - only if not exists
CREATE TABLE IF NOT EXISTS commitments (
  id BIGSERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  date DATE NOT NULL,
  time TIME NOT NULL,
  status TEXT NOT NULL DEFAULT 'pendente' CHECK (status IN ('confirmado', 'pendente', 'cancelado')),
  location TEXT,
  attendees TEXT[] DEFAULT '{}',
  project_id BIGINT REFERENCES projects(id),
  project_name TEXT,
  demand_id BIGINT,
  demand_title TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create demands table (solicitações/demandas) - only if not exists
CREATE TABLE IF NOT EXISTS demands (
  id BIGSERIAL PRIMARY KEY,
  citizen_id BIGINT REFERENCES citizens(id),
  citizen_name TEXT NOT NULL,
  subject TEXT NOT NULL,
  description TEXT NOT NULL,
  type TEXT NOT NULL DEFAULT 'oficio' CHECK (type IN ('oficio', 'indicacao', 'requerimento', 'emenda')),
  priority TEXT NOT NULL DEFAULT 'media' CHECK (priority IN ('alta', 'media', 'baixa')),
  status TEXT NOT NULL DEFAULT 'protocolado' CHECK (status IN ('protocolado', 'em_andamento', 'resolvido', 'cancelado')),
  responsible_staff TEXT,
  project_id BIGINT REFERENCES projects(id),
  deadline DATE,
  resolution_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create team_members table - only if not exists
CREATE TABLE IF NOT EXISTS team_members (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  role TEXT NOT NULL,
  area TEXT NOT NULL,
  rating DECIMAL(2,1) DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
  email TEXT NOT NULL UNIQUE,
  phone TEXT NOT NULL,
  tasks_today INTEGER DEFAULT 0,
  tasks_week INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create triggers for updated_at (drop if exists first)
DROP TRIGGER IF EXISTS update_commitments_updated_at ON commitments;
DROP TRIGGER IF EXISTS update_demands_updated_at ON demands;
DROP TRIGGER IF EXISTS update_team_members_updated_at ON team_members;

CREATE TRIGGER update_commitments_updated_at BEFORE UPDATE ON commitments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_demands_updated_at BEFORE UPDATE ON demands FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_team_members_updated_at BEFORE UPDATE ON team_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS (ignore if already enabled)
DO $$ 
BEGIN
    ALTER TABLE commitments ENABLE ROW LEVEL SECURITY;
EXCEPTION WHEN OTHERS THEN
    NULL;
END $$;

DO $$ 
BEGIN
    ALTER TABLE demands ENABLE ROW LEVEL SECURITY;
EXCEPTION WHEN OTHERS THEN
    NULL;
END $$;

DO $$ 
BEGIN
    ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;
EXCEPTION WHEN OTHERS THEN
    NULL;
END $$;

-- Create policies (drop if exists first)
DROP POLICY IF EXISTS "Allow all operations on commitments" ON commitments;
DROP POLICY IF EXISTS "Allow all operations on demands" ON demands;
DROP POLICY IF EXISTS "Allow all operations on team_members" ON team_members;

CREATE POLICY "Allow all operations on commitments" ON commitments FOR ALL USING (true);
CREATE POLICY "Allow all operations on demands" ON demands FOR ALL USING (true);
CREATE POLICY "Allow all operations on team_members" ON team_members FOR ALL USING (true);