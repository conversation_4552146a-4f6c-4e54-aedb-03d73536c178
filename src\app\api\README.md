# API Routes - ProMandato

## Estrutura

### 🤖 IA e Chat
- `/api/ai` - API multi-tenant principal
- `/api/ai/chat` - Rota de compatibilidade

### 👥 Administração
- `/api/admin/tenants` - Gerenciamento de tenants e API keys

### 🔐 Autenticação
- `/api/auth/signup` - Cadastro de usuários

## Uso

A API principal `/api/ai` requer autenticação via Bearer token (API key do tenant).

### Para código legado:
```javascript
// Funciona mas redireciona internamente para /api/ai
fetch('/api/ai/chat', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    messages: [{ role: 'user', content: 'Sua pergunta' }]
  })
});
```

## Migração

1. **Imediato**: Código existente continua funcionando
2. **Recomendado**: Migrar gradualmente para `/api/ai`
3. **Futuro**: Remover `/api/ai/chat` quando não houver mais uso