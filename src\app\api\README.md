# API Routes - ProMandato

## Estrutura Organizada

### 🤖 IA e Chat
- `/api/ai` - **Nova API multi-tenant** (principal)
- `/api/ai/chat` - Rota de compatibilidade (redireciona para `/api/ai`)

### 👥 Administração
- `/api/admin/tenants` - Gerenciamento de tenants e API keys

## Uso Recomendado

### Para novos desenvolvimentos:
```javascript
// Use sempre a nova API multi-tenant
fetch('/api/ai', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer sua_api_key_aqui',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    page: 'dashboard',
    messages: [{ role: 'user', content: 'Sua pergunta' }]
  })
});
```

### Para código legado:
```javascript
// Funciona mas redireciona internamente para /api/ai
fetch('/api/ai/chat', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    messages: [{ role: 'user', content: 'Sua pergunta' }]
  })
});
```

## Migração

1. **Imediato**: Código existente continua funcionando
2. **Recomendado**: Migrar gradualmente para `/api/ai`
3. **Futuro**: Remover `/api/ai/chat` quando não houver mais uso