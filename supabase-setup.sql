-- Criar tabelas para sistema multi-tenant

-- Tabela de tenants (gabinetes)
create table if not exists tenants (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  slug text unique not null,
  created_at timestamp with time zone default now()
);

-- Tabela de API keys dos tenants
create table if not exists tenant_api_keys (
  id uuid primary key default gen_random_uuid(),
  tenant_id uuid references tenants(id) on delete cascade,
  api_key text unique not null,
  is_active boolean default true,
  created_at timestamp with time zone default now()
);

-- Tabela de contextos por página
create table if not exists contexts (
  id uuid primary key default gen_random_uuid(),
  tenant_id uuid references tenants(id) on delete cascade,
  page text not null,
  content text,
  created_at timestamp with time zone default now(),
  unique(tenant_id, page)
);

-- Tabela de templates de prompt
create table if not exists prompt_templates (
  id uuid primary key default gen_random_uuid(),
  tenant_id uuid references tenants(id) on delete cascade,
  page text,
  system_prompt text not null,
  created_at timestamp with time zone default now(),
  unique(tenant_id, page)
);

-- Inserir dados de exemplo
insert into tenants (name, slug) values 
('Gabinete Exemplo', 'gabinete-exemplo');

-- Gerar API key para o tenant (chave fixa para teste)
insert into tenant_api_keys (tenant_id, api_key)
select id, 'gbc_exemplo_test_key_123'
from tenants where slug = 'gabinete-exemplo';

-- Inserir contexto
insert into contexts (tenant_id, page, content) 
select id, 'propostas', 'Contexto: Este gabinete foca em educação, saúde e infraestrutura urbana.'
from tenants where slug = 'gabinete-exemplo';

-- Inserir prompt template
insert into prompt_templates (tenant_id, page, system_prompt)
select id, 'propostas', 'Você é um assistente especializado em propostas legislativas brasileiras. Foque em soluções práticas e viáveis para educação, saúde e infraestrutura. Sempre responda em português brasileiro.'
from tenants where slug = 'gabinete-exemplo';

-- Consultar API key gerada
select t.name, t.slug, k.api_key 
from tenants t 
join tenant_api_keys k on t.id = k.tenant_id 
where t.slug = 'gabinete-exemplo';