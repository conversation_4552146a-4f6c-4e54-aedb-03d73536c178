-- <PERSON><PERSON><PERSON> tabelas para sistema multi-tenant

-- Tabela de tenants (gabinetes)
create table if not exists tenants (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  slug text unique not null,
  created_at timestamp with time zone default now()
);

-- Tabela de API keys dos tenants
create table if not exists tenant_api_keys (
  id uuid primary key default gen_random_uuid(),
  tenant_id uuid references tenants(id) on delete cascade,
  api_key text unique not null,
  is_active boolean default true,
  created_at timestamp with time zone default now()
);

-- Tabela de contextos por página
create table if not exists contexts (
  id uuid primary key default gen_random_uuid(),
  tenant_id uuid references tenants(id) on delete cascade,
  page text not null,
  content text,
  created_at timestamp with time zone default now(),
  unique(tenant_id, page)
);

-- Tabela de templates de prompt
create table if not exists prompt_templates (
  id uuid primary key default gen_random_uuid(),
  tenant_id uuid references tenants(id) on delete cascade,
  page text,
  system_prompt text not null,
  created_at timestamp with time zone default now(),
  unique(tenant_id, page)
);

-- Nota: Os dados serão criados automaticamente quando os usuários se cadastrarem