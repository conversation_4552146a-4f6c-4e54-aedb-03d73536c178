interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

const store: RateLimitStore = {};

export function rateLimit(
  identifier: string,
  limit: number = 10,
  windowMs: number = 60000 // 1 minuto
): { success: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const key = identifier;

  // Limpar entradas expiradas
  if (store[key] && now > store[key].resetTime) {
    delete store[key];
  }

  // Inicializar se não existir
  if (!store[key]) {
    store[key] = {
      count: 0,
      resetTime: now + windowMs
    };
  }

  // Incrementar contador
  store[key].count++;

  const remaining = Math.max(0, limit - store[key].count);
  const success = store[key].count <= limit;

  return {
    success,
    remaining,
    resetTime: store[key].resetTime
  };
}

export function getRateLimitHeaders(
  limit: number,
  remaining: number,
  resetTime: number
) {
  return {
    'X-RateLimit-Limit': limit.toString(),
    'X-RateLimit-Remaining': remaining.toString(),
    'X-RateLimit-Reset': Math.ceil(resetTime / 1000).toString()
  };
}