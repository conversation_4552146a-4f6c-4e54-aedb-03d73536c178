"use client";
import { BarChart3, Calendar as CalendarIcon, Users, FileSignature, Gavel, Megaphone, TrendingUp, UserCheck, Settings, Bot, FileText } from 'lucide-react';

export const Sidebar = ({ currentView, setCurrentView, setSidebarOpen, darkMode, sidebarOpen }) => {
  const menuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { id: 'agenda', label: 'Agenda', icon: CalendarIcon },
    { id: 'citizens', label: 'Cidadãos', icon: Users },
    { id: 'requests', label: 'Demandas', icon: FileSignature },
    { id: 'projects', label: 'Projetos', icon: Gavel },
    { id: 'communications', label: 'Comunicação', icon: Megaphone },
    { id: 'analytics', label: 'Analytics', icon: TrendingUp },
    { id: 'reports', label: 'Relatórios', icon: FileText },
    { id: 'ai', label: 'IA Central', icon: <PERSON><PERSON> },
    { id: 'team', label: 'Equipe', icon: User<PERSON><PERSON><PERSON> },
    { id: 'settings', label: 'Configurações', icon: Settings }
  ];

  return (
    <aside className={`fixed inset-y-0 left-0 z-50 w-64 ${darkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg transform transition-all duration-300 ease-in-out ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} md:translate-x-0 md:static md:inset-0 md:z-0`}>
      <nav className="flex-1 p-4 space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon;
          return (
            <button
              key={item.id}
              onClick={() => {
                setCurrentView(item.id);
                setSidebarOpen(false);
              }}
              className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${currentView === item.id 
                ? (darkMode ? 'bg-blue-900/30 text-blue-400' : 'bg-blue-100 text-blue-700')
                : (darkMode ? 'text-gray-300 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100')
              }`}
            >
              <Icon className="w-5 h-5" />
              <span>{item.label}</span>
            </button>
          );
        })}
      </nav>
    </aside>
  );
};
