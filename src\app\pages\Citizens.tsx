"use client";
import { useState, useEffect } from 'react';
import { UserPlus, Search, Filter, Users, MapPin, Phone, Mail, Upload, Download, FileText } from 'lucide-react';
import { NewCitizenModal } from './NewCitizenModal';
import { CitizenDetailsModal } from './CitizenDetailsModal';
import { citizenService } from '@/services/citizenService';

interface Citizen {
  id: number;
  name: string;
  email: string;
  phone: string;
  cpf: string;
  birthDate: string;
  address: {
    street: string;
    number: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
  };
  socialMedia: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
    whatsapp?: string;
  };
  category: string;
  tags: string[];
  notes: string;
  lastContact: string;
  status: 'active' | 'inactive' | 'blocked';
}

export const Citizens = ({ setSelectedCitizen, darkMode }: { setSelectedCitizen: (citizen: Citizen) => void, darkMode: boolean }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isImportModalOpen, setIsImportModalOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');
    const [selectedStatus, setSelectedStatus] = useState('all');
    const [citizens, setCitizens] = useState<Citizen[]>([]);
    const [loading, setLoading] = useState(true);
    const [importing, setImporting] = useState(false);
    const [selectedCitizenForDetails, setSelectedCitizenForDetails] = useState<Citizen | null>(null);

    useEffect(() => {
        loadCitizens();
    }, []);

    const loadCitizens = async () => {
        try {
            const data = await citizenService.getAll();
            setCitizens(data || []);
        } catch (error) {
            console.error('Error loading citizens:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleCSVImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file) return;

        setImporting(true);
        try {
            const text = await file.text();
            const lines = text.split('\n').filter(line => line.trim());
            const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
            
            const importedContacts = [];
            
            for (let i = 1; i < lines.length; i++) {
                const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
                if (values.length < 2) continue;
                
                const contact = {
                    name: '',
                    phone: '',
                    email: '',
                    cpf: '',
                    birth_date: '',
                    address: {},
                    social_media: {},
                    category: 'eleitor',
                    tags: [],
                    notes: 'Importado via CSV',
                    last_contact: new Date().toISOString().split('T')[0],
                    status: 'active' as const
                };
                
                headers.forEach((header, index) => {
                    const value = values[index] || '';
                    if (!value) return;
                    
                    if (header.includes('nome') || header.includes('name')) {
                        contact.name = value;
                    } else if (header.includes('telefone') || header.includes('phone') || header.includes('whatsapp')) {
                        contact.phone = value.replace(/\D/g, '');
                        contact.social_media = { whatsapp: value.replace(/\D/g, '') };
                    } else if (header.includes('email') || header.includes('e-mail')) {
                        contact.email = value;
                    }
                });
                
                if (contact.name && contact.phone) {
                    // Gerar email único se não fornecido
                    if (!contact.email) {
                        const phoneDigits = contact.phone.slice(-8);
                        contact.email = `contato${phoneDigits}@importado.local`;
                    }
                    importedContacts.push(contact);
                }
            }
            
            // Salvar contatos importados
            let successCount = 0;
            for (const contact of importedContacts) {
                try {
                    await citizenService.create(contact);
                    successCount++;
                } catch (error: any) {
                    // Se email já existe, tenta com timestamp
                    if (error.message?.includes('duplicate') || error.code === '23505') {
                        try {
                            const timestamp = Date.now();
                            contact.email = `${contact.email.split('@')[0]}_${timestamp}@importado.local`;
                            await citizenService.create(contact);
                            successCount++;
                        } catch (retryError) {
                            console.error('Erro ao importar contato:', contact.name, retryError);
                        }
                    } else {
                        console.error('Erro ao importar contato:', contact.name, error);
                    }
                }
            }
            
            await loadCitizens();
            setIsImportModalOpen(false);
            alert(`${successCount} de ${importedContacts.length} contatos importados com sucesso!`);
            
        } catch (error) {
            console.error('Erro ao importar CSV:', error);
            alert('Erro ao importar arquivo. Verifique o formato e tente novamente.');
        } finally {
            setImporting(false);
            event.target.value = '';
        }
    };

    const downloadSampleCSV = () => {
        const sampleData = [
            ['Nome', 'Telefone', 'Email'],
            ['João Silva', '11999887766', '<EMAIL>'],
            ['Maria Santos', '11888776655', '<EMAIL>']
        ];
        
        const csvContent = sampleData.map(row => row.join(',')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'exemplo-contatos.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    };

    const categories = ['all', 'eleitor', 'liderança', 'empresário', 'funcionário público', 'estudante', 'apoiador'];
    const statuses = ['all', 'active', 'inactive', 'blocked'];

    const filteredCitizens = citizens.filter(citizen => {
        const matchesSearch = citizen.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            citizen.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            citizen.phone.includes(searchTerm);
        const matchesCategory = selectedCategory === 'all' || citizen.category === selectedCategory;
        const matchesStatus = selectedStatus === 'all' || citizen.status === selectedStatus;
        return matchesSearch && matchesCategory && matchesStatus;
    });

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'inactive': return 'bg-yellow-100 text-yellow-800';
            case 'blocked': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                    <h1 className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Cidadãos e Contatos</h1>
                    <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'}`}>
                        <Users className="w-4 h-4" />
                        <span className="text-sm font-medium">{filteredCitizens.length} contatos</span>
                    </div>
                </div>
                <div className="flex items-center space-x-3">
                    <button onClick={() => setIsImportModalOpen(true)} className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2">
                        <Upload className="w-4 h-4" />
                        <span>Importar CSV</span>
                    </button>
                    <button onClick={() => setIsModalOpen(true)} className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
                        <UserPlus className="w-4 h-4" />
                        <span>Novo Contato</span>
                    </button>
                </div>
            </div>

            <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                <div className="flex flex-col md:flex-row gap-4 mb-6">
                    <div className="relative flex-1">
                        <Search className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                        <input
                            type="text"
                            placeholder="Buscar por nome, email ou telefone..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className={`w-full pl-10 pr-4 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100 placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'}`}
                        />
                    </div>
                    <select
                        value={selectedCategory}
                        onChange={(e) => setSelectedCategory(e.target.value)}
                        className={`px-4 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                    >
                        {categories.map(cat => (
                            <option key={cat} value={cat}>{cat === 'all' ? 'Todas as categorias' : cat}</option>
                        ))}
                    </select>
                    <select
                        value={selectedStatus}
                        onChange={(e) => setSelectedStatus(e.target.value)}
                        className={`px-4 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                    >
                        {statuses.map(status => (
                            <option key={status} value={status}>{status === 'all' ? 'Todos os status' : status}</option>
                        ))}
                    </select>
                </div>

                <div className="overflow-x-auto">
                    <table className={`w-full ${darkMode ? 'text-gray-300' : 'text-gray-900'}`}>
                        <thead>
                            <tr className={`border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                                <th className={`text-left py-3 px-4 font-medium ${darkMode ? 'text-gray-200' : 'text-gray-700'}`}>Nome</th>
                                <th className={`text-left py-3 px-4 font-medium ${darkMode ? 'text-gray-200' : 'text-gray-700'}`}>Email</th>
                                <th className={`text-left py-3 px-4 font-medium ${darkMode ? 'text-gray-200' : 'text-gray-700'}`}>Telefone</th>
                                <th className={`text-left py-3 px-4 font-medium ${darkMode ? 'text-gray-200' : 'text-gray-700'}`}>Categoria</th>
                                <th className={`text-left py-3 px-4 font-medium ${darkMode ? 'text-gray-200' : 'text-gray-700'}`}>Cidade</th>
                                <th className={`text-left py-3 px-4 font-medium ${darkMode ? 'text-gray-200' : 'text-gray-700'}`}>Status</th>
                                <th className={`text-left py-3 px-4 font-medium ${darkMode ? 'text-gray-200' : 'text-gray-700'}`}>Último Contato</th>
                            </tr>
                        </thead>
                        <tbody>
                            {filteredCitizens.map(citizen => (
                                <tr 
                                    key={citizen.id}
                                    onClick={() => setSelectedCitizenForDetails(citizen)}
                                    className={`border-b cursor-pointer transition-colors ${
                                        darkMode 
                                            ? 'border-gray-700 hover:bg-gray-700' 
                                            : 'border-gray-100 hover:bg-gray-50'
                                    }`}
                                >
                                    <td className="py-3 px-4">
                                        <div className="font-medium">{citizen.name}</div>
                                    </td>
                                    <td className="py-3 px-4">
                                        <div className="text-sm truncate max-w-48">{citizen.email}</div>
                                    </td>
                                    <td className="py-3 px-4">
                                        <div className="text-sm">{citizen.phone}</div>
                                    </td>
                                    <td className="py-3 px-4">
                                        <div className="text-sm capitalize">{citizen.category}</div>
                                    </td>
                                    <td className="py-3 px-4">
                                        <div className="text-sm">{citizen.address?.city || 'N/A'}</div>
                                    </td>
                                    <td className="py-3 px-4">
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(citizen.status)}`}>
                                            {citizen.status}
                                        </span>
                                    </td>
                                    <td className="py-3 px-4">
                                        <div className="text-sm">
                                            {new Date(citizen.lastContact).toLocaleDateString('pt-BR')}
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {loading ? (
                    <div className={`text-center py-12 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                        <p>Carregando contatos...</p>
                    </div>
                ) : filteredCitizens.length === 0 ? (
                    <div className={`text-center py-12 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p>Nenhum contato encontrado</p>
                    </div>
                ) : null}
            </div>
            <NewCitizenModal isOpen={isModalOpen} setIsOpen={setIsModalOpen} darkMode={darkMode} onSave={loadCitizens} />
            <CitizenDetailsModal 
                citizen={selectedCitizenForDetails}
                isOpen={!!selectedCitizenForDetails}
                onClose={() => setSelectedCitizenForDetails(null)}
                darkMode={darkMode}
                onUpdate={loadCitizens}
            />
            
            {/* Modal de Importação CSV */}
            {isImportModalOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto`}>
                        <div className="flex items-center justify-between mb-6">
                            <h2 className={`text-xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
                                Importar Contatos do WhatsApp
                            </h2>
                            <button 
                                onClick={() => setIsImportModalOpen(false)}
                                className={`${darkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-500 hover:text-gray-700'}`}
                            >
                                ×
                            </button>
                        </div>
                        
                        <div className="space-y-6">
                            {/* Instruções para Android */}
                            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-blue-50'}`}>
                                <h3 className={`font-semibold mb-3 flex items-center ${darkMode ? 'text-blue-400' : 'text-blue-700'}`}>
                                    <Phone className="w-5 h-5 mr-2" />
                                    Android (Google Contatos)
                                </h3>
                                <ol className={`list-decimal list-inside space-y-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                                    <li>Abra o app <strong>Contatos do Google</strong></li>
                                    <li>Toque no menu (☰) → <strong>Configurações</strong></li>
                                    <li>Selecione <strong>Exportar</strong></li>
                                    <li>Escolha <strong>Exportar para arquivo .vcf</strong></li>
                                    <li>Acesse <strong>contacts.google.com</strong> no computador</li>
                                    <li>Clique em <strong>Exportar</strong> → <strong>CSV do Google</strong></li>
                                    <li>Faça o upload do arquivo aqui</li>
                                </ol>
                            </div>
                            
                            {/* Instruções para iPhone */}
                            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-green-50'}`}>
                                <h3 className={`font-semibold mb-3 flex items-center ${darkMode ? 'text-green-400' : 'text-green-700'}`}>
                                    <Phone className="w-5 h-5 mr-2" />
                                    iPhone (iCloud)
                                </h3>
                                <ol className={`list-decimal list-inside space-y-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                                    <li>Acesse <strong>icloud.com</strong> no computador</li>
                                    <li>Faça login com sua Apple ID</li>
                                    <li>Clique em <strong>Contatos</strong></li>
                                    <li>Selecione todos os contatos (Ctrl+A ou Cmd+A)</li>
                                    <li>Clique na engrenagem (⚙️) → <strong>Exportar vCard</strong></li>
                                    <li>Use um conversor online para transformar .vcf em .csv</li>
                                    <li>Ou use o formato de exemplo abaixo</li>
                                </ol>
                            </div>
                            
                            {/* Formato do CSV */}
                            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-yellow-50'}`}>
                                <h3 className={`font-semibold mb-3 flex items-center ${darkMode ? 'text-yellow-400' : 'text-yellow-700'}`}>
                                    <FileText className="w-5 h-5 mr-2" />
                                    Formato do Arquivo CSV
                                </h3>
                                <p className={`text-sm mb-3 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                                    O arquivo deve ter as colunas: <strong>Nome, Telefone, Email</strong>
                                </p>
                                <div className={`p-3 rounded border font-mono text-xs ${darkMode ? 'bg-gray-800 border-gray-600 text-gray-300' : 'bg-white border-gray-300 text-gray-800'}`}>
                                    Nome,Telefone,Email<br/>
                                    João Silva,11999887766,<EMAIL><br/>
                                    Maria Santos,11888776655,<EMAIL>
                                </div>
                                <button 
                                    onClick={downloadSampleCSV}
                                    className={`mt-3 text-sm flex items-center space-x-1 ${darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-600 hover:text-blue-700'}`}
                                >
                                    <Download className="w-4 h-4" />
                                    <span>Baixar arquivo de exemplo</span>
                                </button>
                            </div>
                            
                            {/* Upload */}
                            <div className={`border-2 border-dashed rounded-lg p-6 text-center ${darkMode ? 'border-gray-600' : 'border-gray-300'}`}>
                                <Upload className={`w-12 h-12 mx-auto mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                                <p className={`mb-4 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                                    Selecione o arquivo CSV com seus contatos
                                </p>
                                <input 
                                    type="file" 
                                    accept=".csv" 
                                    onChange={handleCSVImport}
                                    disabled={importing}
                                    className="hidden" 
                                    id="csv-upload"
                                />
                                <label 
                                    htmlFor="csv-upload" 
                                    className={`inline-flex items-center space-x-2 px-4 py-2 rounded-lg cursor-pointer ${
                                        importing 
                                            ? 'bg-gray-400 cursor-not-allowed' 
                                            : 'bg-blue-600 hover:bg-blue-700'
                                    } text-white`}
                                >
                                    {importing ? (
                                        <>
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                            <span>Importando...</span>
                                        </>
                                    ) : (
                                        <>
                                            <Upload className="w-4 h-4" />
                                            <span>Escolher Arquivo CSV</span>
                                        </>
                                    )}
                                </label>
                            </div>
                            
                            <div className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                <p><strong>Dica:</strong> Os contatos serão importados com status "ativo" e categoria "eleitor". Você pode editar essas informações depois.</p>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};