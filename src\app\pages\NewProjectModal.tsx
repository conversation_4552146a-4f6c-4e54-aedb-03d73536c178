"use client";
import { useState } from 'react';
import { But<PERSON> } from "@/app/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Textarea } from "@/app/components/ui/textarea";
import { projectService } from '@/services/projectService';

interface NewProjectModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  darkMode: boolean;
  onSave?: () => void;
}

export const NewProjectModal = ({ isOpen, setIsOpen, darkMode, onSave }: NewProjectModalProps) => {
    const [formData, setFormData] = useState({
        name: '',
        protocol: '',
        category: '',
        description: ''
    });
    const [budget, setBudget] = useState('');
    const [loading, setLoading] = useState(false);

    const formatCurrency = (value: string) => {
        const numericValue = value.replace(/\D/g, '');
        const formattedValue = new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
            minimumFractionDigits: 2
        }).format(Number(numericValue) / 100);
        return formattedValue;
    };

    const handleBudgetChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const formatted = formatCurrency(e.target.value);
        setBudget(formatted);
    };

    const parseCurrency = (value: string) => {
        return Number(value.replace(/[^\d,]/g, '').replace(',', '.')) || 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        try {
            await projectService.create({
                name: formData.name,
                protocol: formData.protocol,
                category: formData.category,
                description: formData.description,
                budget: parseCurrency(budget),
                status: 'em_andamento'
            });
            setIsOpen(false);
            setFormData({ name: '', protocol: '', category: '', description: '' });
            setBudget('');
            onSave?.();
        } catch (error) {
            console.error('Error creating project:', error);
        } finally {
            setLoading(false);
        }
    };
    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className={`${darkMode ? 'bg-gray-800 border-gray-700' : ''}`}>
                <DialogHeader>
                    <DialogTitle className={`${darkMode ? 'text-white' : ''}`}>Novo Projeto</DialogTitle>
                    <DialogDescription className={`${darkMode ? 'text-gray-400' : ''}`}>
                        Preencha os detalhes do novo projeto.
                    </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleSubmit}>
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="name" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                                Nome
                            </Label>
                            <Input 
                                id="name" 
                                value={formData.name}
                                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                                className={`col-span-3 ${darkMode ? 'text-gray-100' : ''}`}
                                required 
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="protocol" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                                Protocolo
                            </Label>
                            <Input 
                                id="protocol" 
                                value={formData.protocol}
                                onChange={(e) => setFormData(prev => ({ ...prev, protocol: e.target.value }))}
                                className={`col-span-3 ${darkMode ? 'text-gray-100' : ''}`}
                                required 
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="category" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                                Categoria
                            </Label>
                            <Input 
                                id="category" 
                                value={formData.category}
                                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                                className={`col-span-3 ${darkMode ? 'text-gray-100' : ''}`}
                                required 
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="budget" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                                Orçamento
                            </Label>
                            <Input 
                                id="budget" 
                                type="text" 
                                value={budget}
                                onChange={handleBudgetChange}
                                placeholder="R$ 0,00"
                                className={`col-span-3 ${darkMode ? 'text-gray-100' : ''}`} 
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="description" className={`text-right ${darkMode ? 'text-gray-300' : ''}`}>
                                Descrição
                            </Label>
                            <Textarea 
                                id="description" 
                                value={formData.description}
                                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                                className={`col-span-3 ${darkMode ? 'text-gray-100' : ''}`} 
                            />
                        </div>
                    </div>
                </form>
                <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>Cancelar</Button>
                    <Button type="submit" onClick={handleSubmit} disabled={loading}>
                        {loading ? 'Salvando...' : 'Salvar'}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};
