"use client";
import { useState } from 'react';
import { But<PERSON> } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Switch } from "@/app/components/ui/switch";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/app/components/ui/card";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs";
import { Textarea } from "@/app/components/ui/textarea";
import { toast } from "sonner";
import { HelpCircle, MessageCircle, Phone, Mail, ExternalLink } from 'lucide-react';
import { OfficeSettings } from './OfficeSettings';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  office: string;
}
export const Settings = ({ user, darkMode, setDarkMode }: { user: User, darkMode: boolean, setDarkMode: (value: boolean) => void }) => {
    const [editedUser, setEditedUser] = useState(user);
    const [notificationSettings, setNotificationSettings] = useState({
        urgent: true,
        reminders: true,
        newRequests: false,
    });

    const handleUserChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setEditedUser(prev => ({ ...prev, [name]: value }));
    };

    const handleNotificationChange = (name: string, value: boolean) => {
        setNotificationSettings(prev => ({ ...prev, [name]: value }));
    };

    const saveUserProfile = () => {
        toast.success('Perfil salvo com sucesso!');
    };
    
    const saveOfficeProfile = () => {
        toast.success('Gabinete salvo com sucesso!');
    };

    const [supportForm, setSupportForm] = useState({
        subject: '',
        message: '',
        priority: 'media'
    });

    const handleSupportSubmit = () => {
        if (!supportForm.subject || !supportForm.message) {
            toast.error('Preencha todos os campos obrigatórios');
            return;
        }
        toast.success('Ticket de suporte enviado com sucesso!');
        setSupportForm({ subject: '', message: '', priority: 'media' });
    };

    return (
        <div className="space-y-6">
            <div>
                <h1 className={`text-3xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Configurações</h1>
                <p className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Gerencie as configurações da sua conta e do sistema.</p>
            </div>

            <Tabs defaultValue="profile" className="w-full">
                <TabsList className={`grid w-full grid-cols-4 ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100'}`}>
                    <TabsTrigger value="profile">Perfil</TabsTrigger>
                    <TabsTrigger value="office">Gabinete</TabsTrigger>
                    <TabsTrigger value="notifications">Notificações</TabsTrigger>
                    <TabsTrigger value="support">Suporte</TabsTrigger>
                </TabsList>
                <TabsContent value="profile">
                    <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : ''}`}>
                        <CardHeader>
                            <CardTitle className={`${darkMode ? 'text-white' : ''}`}>Perfil</CardTitle>
                            <CardDescription className={`${darkMode ? 'text-gray-400' : ''}`}>
                                Edite suas informações pessoais.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="name" className={`${darkMode ? 'text-gray-300' : ''}`}>Nome</Label>
                                <Input id="name" name="name" value={editedUser.name} onChange={handleUserChange} className={`${darkMode ? 'bg-gray-700 border-gray-600 text-white' : ''}`} />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="role" className={`${darkMode ? 'text-gray-300' : ''}`}>Cargo</Label>
                                <Input id="role" name="role" value={editedUser.role} onChange={handleUserChange} className={`${darkMode ? 'bg-gray-700 border-gray-600 text-white' : ''}`} />
                            </div>
                        </CardContent>
                        <CardFooter>
                            <Button onClick={saveUserProfile}>Salvar Alterações</Button>
                        </CardFooter>
                    </Card>
                </TabsContent>
                <TabsContent value="office">
                    <OfficeSettings darkMode={darkMode} />
                </TabsContent>
                <TabsContent value="notifications">
                    <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : ''}`}>
                        <CardHeader>
                            <CardTitle className={`${darkMode ? 'text-white' : ''}`}>Notificações</CardTitle>
                            <CardDescription className={`${darkMode ? 'text-gray-400' : ''}`}>
                                Gerencie como você recebe as notificações.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <Label htmlFor="urgent-notifications" className={`${darkMode ? 'text-gray-300' : ''}`}>Notificações Urgentes</Label>
                                <Switch id="urgent-notifications" checked={notificationSettings.urgent} onCheckedChange={(value) => handleNotificationChange('urgent', value)} />
                            </div>
                            <div className="flex items-center justify-between">
                                <Label htmlFor="reminder-notifications" className={`${darkMode ? 'text-gray-300' : ''}`}>Lembretes de Agenda</Label>
                                <Switch id="reminder-notifications" checked={notificationSettings.reminders} onCheckedChange={(value) => handleNotificationChange('reminders', value)} />
                            </div>
                            <div className="flex items-center justify-between">
                                <Label htmlFor="new-requests" className={`${darkMode ? 'text-gray-300' : ''}`}>Novas Demandas</Label>
                                <Switch id="new-requests" checked={notificationSettings.newRequests} onCheckedChange={(value) => handleNotificationChange('newRequests', value)} />
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
                <TabsContent value="support">
                    <div className="space-y-6">
                        <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : ''}`}>
                            <CardHeader>
                                <CardTitle className={`${darkMode ? 'text-white' : ''} flex items-center space-x-2`}>
                                    <HelpCircle className="w-5 h-5" />
                                    <span>Canais de Suporte</span>
                                </CardTitle>
                                <CardDescription className={`${darkMode ? 'text-gray-400' : ''}`}>
                                    Entre em contato conosco através dos canais disponíveis.
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className={`p-4 border rounded-lg ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                                        <div className="flex items-center space-x-3 mb-2">
                                            <Phone className={`w-5 h-5 ${darkMode ? 'text-blue-400' : 'text-blue-600'}`} />
                                            <h3 className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>Telefone</h3>
                                        </div>
                                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Suporte técnico</p>
                                        <p className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>(61) 3000-0000</p>
                                        <p className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>Seg-Sex: 8h às 18h</p>
                                    </div>

                                    <div className={`p-4 border rounded-lg ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                                        <div className="flex items-center space-x-3 mb-2">
                                            <Mail className={`w-5 h-5 ${darkMode ? 'text-green-400' : 'text-green-600'}`} />
                                            <h3 className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>Email</h3>
                                        </div>
                                        <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Suporte geral</p>
                                        <p className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}><EMAIL></p>
                                        <p className={`text-xs ${darkMode ? 'text-gray-500' : 'text-gray-500'}`}>Resposta em até 24h</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className={`${darkMode ? 'bg-gray-800 border-gray-700' : ''}`}>
                            <CardHeader>
                                <CardTitle className={`${darkMode ? 'text-white' : ''}`}>Abrir Ticket de Suporte</CardTitle>
                                <CardDescription className={`${darkMode ? 'text-gray-400' : ''}`}>
                                    Descreva seu problema ou dúvida e nossa equipe entrará em contato.
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="subject" className={`${darkMode ? 'text-gray-300' : ''}`}>Assunto *</Label>
                                        <Input 
                                            id="subject" 
                                            value={supportForm.subject}
                                            onChange={(e) => setSupportForm(prev => ({ ...prev, subject: e.target.value }))}
                                            placeholder="Descreva brevemente o problema"
                                            className={`${darkMode ? 'bg-gray-700 border-gray-600 text-white' : ''}`} 
                                        />
                                    </div>
                                    <div className="space-y-2">
                                        <Label htmlFor="priority" className={`${darkMode ? 'text-gray-300' : ''}`}>Prioridade</Label>
                                        <select
                                            id="priority"
                                            value={supportForm.priority}
                                            onChange={(e) => setSupportForm(prev => ({ ...prev, priority: e.target.value }))}
                                            className={`w-full px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300'}`}
                                        >
                                            <option value="baixa">Baixa</option>
                                            <option value="media">Média</option>
                                            <option value="alta">Alta</option>
                                            <option value="urgente">Urgente</option>
                                        </select>
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="message" className={`${darkMode ? 'text-gray-300' : ''}`}>Mensagem *</Label>
                                    <Textarea 
                                        id="message"
                                        value={supportForm.message}
                                        onChange={(e) => setSupportForm(prev => ({ ...prev, message: e.target.value }))}
                                        placeholder="Descreva detalhadamente o problema, incluindo passos para reproduzir se aplicável..."
                                        rows={5}
                                        className={`${darkMode ? 'bg-gray-700 border-gray-600 text-white' : ''}`}
                                    />
                                </div>
                            </CardContent>
                            <CardFooter>
                                <Button onClick={handleSupportSubmit} className="w-full md:w-auto">
                                    Enviar Ticket
                                </Button>
                            </CardFooter>
                        </Card>
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    );
};
