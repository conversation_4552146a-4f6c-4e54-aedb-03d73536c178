import { supabase } from '@/lib/supabase'
import { Database } from '@/types/database'

type Demand = Database['public']['Tables']['demands']['Row']
type DemandInsert = Database['public']['Tables']['demands']['Insert']
type DemandUpdate = Database['public']['Tables']['demands']['Update']

export const demandService = {
  async getAll() {
    const { data, error } = await supabase
      .from('demands')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  async create(demand: DemandInsert) {
    const { data, error } = await supabase
      .from('demands')
      .insert(demand)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async update(id: number, updates: DemandUpdate) {
    const { data, error } = await supabase
      .from('demands')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async delete(id: number) {
    const { error } = await supabase
      .from('demands')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}