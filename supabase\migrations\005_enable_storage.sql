-- Enable Storage
INSERT INTO storage.buckets (id, name, public) VALUES 
  ('office-assets', 'office-assets', true),
  ('user-avatars', 'user-avatars', true);

-- Create storage policies for office-assets bucket
CREATE POLICY "Allow public read access on office-assets" ON storage.objects
  FOR SELECT USING (bucket_id = 'office-assets');

CREATE POLICY "Allow authenticated upload on office-assets" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'office-assets');

CREATE POLICY "Allow authenticated update on office-assets" ON storage.objects
  FOR UPDATE USING (bucket_id = 'office-assets');

CREATE POLICY "Allow authenticated delete on office-assets" ON storage.objects
  FOR DELETE USING (bucket_id = 'office-assets');

-- Create storage policies for user-avatars bucket
CREATE POLICY "Allow public read access on user-avatars" ON storage.objects
  FOR SELECT USING (bucket_id = 'user-avatars');

CREATE POLICY "Allow authenticated upload on user-avatars" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'user-avatars');

CREATE POLICY "Allow authenticated update on user-avatars" ON storage.objects
  FOR UPDATE USING (bucket_id = 'user-avatars');

CREATE POLICY "Allow authenticated delete on user-avatars" ON storage.objects
  FOR DELETE USING (bucket_id = 'user-avatars');