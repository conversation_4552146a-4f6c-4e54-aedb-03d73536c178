"use client";
import { useState, useEffect, useRef } from 'react';
import { Search, User, FileText, Target, X } from 'lucide-react';
import { citizenService } from '@/services/citizenService';
import { demandService } from '@/services/demandService';
import { projectService } from '@/services/projectService';

interface SearchResult {
  id: number;
  type: 'citizen' | 'demand' | 'project';
  title: string;
  subtitle: string;
  data: any;
}

export const SearchBar = ({ darkMode }: { darkMode: boolean }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (query.length < 2) {
      setResults([]);
      setIsOpen(false);
      return;
    }

    const searchTimeout = setTimeout(async () => {
      setLoading(true);
      try {
        const [citizens, demands, projects] = await Promise.all([
          citizenService.getAll(),
          demandService.getAll(),
          projectService.getAll()
        ]);

        const searchResults: SearchResult[] = [];
        const lowerQuery = query.toLowerCase();

        // Buscar cidadãos
        citizens?.forEach(citizen => {
          if (citizen.name.toLowerCase().includes(lowerQuery) || 
              citizen.email.toLowerCase().includes(lowerQuery) ||
              citizen.phone.includes(query)) {
            searchResults.push({
              id: citizen.id,
              type: 'citizen',
              title: citizen.name,
              subtitle: `${citizen.phone} • ${citizen.email}`,
              data: citizen
            });
          }
        });

        // Buscar demandas
        demands?.forEach(demand => {
          if (demand.subject.toLowerCase().includes(lowerQuery) ||
              demand.citizen_name.toLowerCase().includes(lowerQuery) ||
              demand.description.toLowerCase().includes(lowerQuery)) {
            searchResults.push({
              id: demand.id,
              type: 'demand',
              title: demand.subject,
              subtitle: `${demand.citizen_name} • ${demand.type}`,
              data: demand
            });
          }
        });

        // Buscar projetos
        projects?.forEach(project => {
          if (project.name.toLowerCase().includes(lowerQuery) ||
              project.protocol.toLowerCase().includes(lowerQuery) ||
              project.description?.toLowerCase().includes(lowerQuery)) {
            searchResults.push({
              id: project.id,
              type: 'project',
              title: project.name,
              subtitle: `${project.protocol} • ${project.category}`,
              data: project
            });
          }
        });

        setResults(searchResults.slice(0, 8));
        setIsOpen(searchResults.length > 0);
      } catch (error) {
        console.error('Erro na busca:', error);
      } finally {
        setLoading(false);
      }
    }, 300);

    return () => clearTimeout(searchTimeout);
  }, [query]);

  const getIcon = (type: string) => {
    switch (type) {
      case 'citizen': return User;
      case 'demand': return FileText;
      case 'project': return Target;
      default: return Search;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'citizen': return 'Cidadão';
      case 'demand': return 'Demanda';
      case 'project': return 'Projeto';
      default: return '';
    }
  };

  const handleResultClick = (result: SearchResult) => {
    setQuery('');
    setIsOpen(false);
    // Aqui você pode implementar navegação ou ação específica
    console.log('Resultado selecionado:', result);
  };

  return (
    <div ref={searchRef} className="relative hidden md:block">
      <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-500" />
      <input
        type="text"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        onFocus={() => query.length >= 2 && results.length > 0 && setIsOpen(true)}
        placeholder="Buscar cidadão, demanda ou projeto..."
        className={`w-64 pl-10 pr-10 py-2 rounded-lg border ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-200' : 'bg-gray-100 border-gray-300 text-gray-800'} focus:outline-none focus:ring-2 focus:ring-blue-500`}
      />
      {query && (
        <button
          onClick={() => {
            setQuery('');
            setIsOpen(false);
          }}
          className="absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-500 hover:text-gray-700"
        >
          <X className="w-4 h-4" />
        </button>
      )}
      
      {isOpen && (
        <div className={`absolute top-full left-0 right-0 mt-1 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto`}>
          {loading ? (
            <div className="p-4 text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
            </div>
          ) : results.length > 0 ? (
            results.map((result) => {
              const Icon = getIcon(result.type);
              return (
                <button
                  key={`${result.type}-${result.id}`}
                  onClick={() => handleResultClick(result)}
                  className={`w-full p-3 text-left hover:${darkMode ? 'bg-gray-700' : 'bg-gray-50'} border-b ${darkMode ? 'border-gray-700' : 'border-gray-100'} last:border-b-0 flex items-center space-x-3`}
                >
                  <Icon className={`w-4 h-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className={`font-medium truncate ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                        {result.title}
                      </p>
                      <span className={`text-xs px-2 py-1 rounded ${darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-600'}`}>
                        {getTypeLabel(result.type)}
                      </span>
                    </div>
                    <p className={`text-sm truncate ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {result.subtitle}
                    </p>
                  </div>
                </button>
              );
            })
          ) : (
            <div className={`p-4 text-center ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Nenhum resultado encontrado
            </div>
          )}
        </div>
      )}
    </div>
  );
};
