-- Create social_posts table
CREATE TABLE social_posts (
  id BIGSERIAL PRIMARY KEY,
  platform TEXT NOT NULL CHECK (platform IN ('Facebook', 'Instagram', 'Twitter', 'LinkedIn')),
  content TEXT NOT NULL,
  likes INTEGER DEFAULT 0,
  comments INTEGER DEFAULT 0,
  shares INTEGER DEFAULT 0,
  scheduled_date TIMESTAMP WITH TIME ZONE,
  published_date TIMESTAMP WITH TIME ZONE,
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'published', 'failed')),
  hashtags TEXT[] DEFAULT '{}',
  mentions TEXT[] DEFAULT '{}',
  media_urls TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create analytics table for tracking metrics
CREATE TABLE social_analytics (
  id BIGSERIAL PRIMARY KEY,
  post_id BIGINT REFERENCES social_posts(id),
  platform TEXT NOT NULL,
  metric_type TEXT NOT NULL CHECK (metric_type IN ('likes', 'comments', 'shares', 'views', 'reach')),
  value INTEGER NOT NULL,
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create triggers
CREATE TRIGGER update_social_posts_updated_at BEFORE UPDATE ON social_posts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS
ALTER TABLE social_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE social_analytics ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Allow all operations on social_posts" ON social_posts FOR ALL USING (true);
CREATE POLICY "Allow all operations on social_analytics" ON social_analytics FOR ALL USING (true);