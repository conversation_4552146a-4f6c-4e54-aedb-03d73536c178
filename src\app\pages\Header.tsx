"use client";
import { Landmark, Menu } from 'lucide-react';
import { SearchBar } from './SearchBar';
import { ThemeToggle } from './ThemeToggle';
import { Notifications } from './Notifications';
import { UserMenu } from './UserMenu';

export const Header = (props: any) => {
  const { user, darkMode, setDarkMode, setSidebarOpen, notifications, unreadNotifications, markNotificationAsRead, markAllAsRead, onProfileClick, onSettingsClick } = props;

  return (
    <header
      className={`${
        darkMode ? "bg-gray-800 border-gray-700" : "bg-white border-gray-200"
      } shadow-sm border-b`}
    >
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSidebarOpen(true)}
              className="md:hidden p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <Menu className="w-6 h-6" />
            </button>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Landmark className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1
                  className={`text-xl font-bold ${
                    darkMode ? "text-gray-100" : "text-gray-900"
                  }`}
                >
                  ProMandato
                </h1>
                <p
                  className={`text-xs ${
                    darkMode ? "text-gray-400" : "text-gray-600"
                  }`}
                >
                  {user?.user_metadata?.office || user?.office || 'Gabinete Demo'}
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <SearchBar darkMode={darkMode} />
            <ThemeToggle darkMode={darkMode} setDarkMode={setDarkMode} />
            <Notifications darkMode={darkMode} />
            <UserMenu
              user={user}
              darkMode={darkMode}
              onProfileClick={onProfileClick}
              onSettingsClick={onSettingsClick}
            />
          </div>
        </div>
      </div>
    </header>
  );
};
