import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Criar novo tenant com API key
export async function POST(request: NextRequest) {
  try {
    const { name, slug } = await request.json();

    // Criar tenant
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .insert({ name, slug })
      .select()
      .single();

    if (tenantError) {
      return NextResponse.json({ error: tenantError.message }, { status: 400 });
    }

    // Gerar API key
    const apiKey = `gbc_${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`;
    
    const { error: keyError } = await supabase
      .from('tenant_api_keys')
      .insert({ tenant_id: tenant.id, api_key: apiKey });

    if (keyError) {
      return NextResponse.json({ error: keyError.message }, { status: 400 });
    }

    return NextResponse.json({ 
      tenant: { id: tenant.id, name: tenant.name, slug: tenant.slug },
      api_key: apiKey 
    });

  } catch (error) {
    return NextResponse.json({ error: 'Erro interno' }, { status: 500 });
  }
}

// Listar tenants
export async function GET() {
  try {
    const { data: tenants } = await supabase
      .from('tenants')
      .select(`
        id, name, slug, created_at,
        tenant_api_keys(api_key, is_active)
      `);

    return NextResponse.json({ tenants });
  } catch (error) {
    return NextResponse.json({ error: 'Erro interno' }, { status: 500 });
  }
}