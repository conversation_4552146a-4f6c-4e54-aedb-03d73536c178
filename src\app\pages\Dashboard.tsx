"use client";
import { useState, useEffect } from 'react';
import { StatCard } from './StatCard';
import { SmartInsights } from './SmartInsights';
import { FileSignature, Calendar as CalendarIcon, Megaphone, Heart } from 'lucide-react';
import { citizenService } from '@/services/citizenService';
import { demandService } from '@/services/demandService';
import { commitmentService } from '@/services/commitmentService';
import { socialPostService } from '@/services/socialPostService';

export const Dashboard = ({ getStatusColor, darkMode }: { getStatusColor: (status: string) => string, darkMode: boolean }) => {
    const [analytics, setAnalytics] = useState({
        newRequests: 0,
        commitmentsToday: 0,
        socialMediaEngagement: 12.5,
        positiveMentions: 85,
        monthlyGrowth: { requests: 0, engagement: 2.1 },
        requestsByArea: {} as Record<string, number>
    });
    const [commitments, setCommitments] = useState<any[]>([]);
    const [citizens, setCitizens] = useState<any[]>([]);
    const [requests, setRequests] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        loadDashboardData();
    }, []);

    const loadDashboardData = async () => {
        try {
            const [citizensData, demandsData, commitmentsData, socialPostsData] = await Promise.all([
                citizenService.getAll(),
                demandService.getAll(),
                commitmentService.getAll(),
                socialPostService.getAll()
            ]);

            const today = new Date().toISOString().split('T')[0];
            const commitmentsToday = commitmentsData?.filter(c => c.date === today).length || 0;

            // Group demands by type for area chart
            const requestsByArea = demandsData?.reduce((acc, demand) => {
                acc[demand.type] = (acc[demand.type] || 0) + 1;
                return acc;
            }, {}) || {};

            // Calculate real social media engagement
            const totalEngagement = socialPostsData?.reduce((sum, post) => 
                sum + (post.likes || 0) + (post.comments || 0) + (post.shares || 0), 0
            ) || 0;
            const avgEngagement = socialPostsData?.length > 0 ? totalEngagement / socialPostsData.length : 0;
            
            // Calculate positive mentions based on engagement ratio
            const totalPosts = socialPostsData?.length || 0;
            const highEngagementPosts = socialPostsData?.filter(post => 
                (post.likes + post.comments + post.shares) > avgEngagement
            ).length || 0;
            const positiveMentions = totalPosts > 0 ? Math.round((highEngagementPosts / totalPosts) * 100) : 0;

            setAnalytics({
                newRequests: demandsData?.length || 0,
                commitmentsToday,
                socialMediaEngagement: avgEngagement,
                positiveMentions,
                monthlyGrowth: { requests: 15, engagement: avgEngagement > 10 ? 5.2 : -2.1 },
                requestsByArea
            });

            setCommitments(commitmentsData || []);
            setCitizens(citizensData || []);
            setRequests(demandsData || []);
        } catch (error) {
            console.error('Error loading dashboard data:', error);
        } finally {
            setLoading(false);
        }
    };
    return (
        <div className="space-y-6">
            <h1 className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Dashboard do Gabinete</h1>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <StatCard title="Novas Demandas (Mês)" value={analytics.newRequests} change={analytics.monthlyGrowth.requests} icon={FileSignature} color="blue" darkMode={darkMode}/>
                <StatCard title="Compromissos Hoje" value={analytics.commitmentsToday} change={0} icon={CalendarIcon} color="green" darkMode={darkMode}/>
                <StatCard title="Engajamento Social" value={analytics.socialMediaEngagement.toFixed(1)} change={analytics.monthlyGrowth.engagement} icon={Megaphone} color="purple" suffix="%" darkMode={darkMode}/>
                <StatCard title="Menções Positivas" value={analytics.positiveMentions} change={0} icon={Heart} color="red" suffix="%" darkMode={darkMode}/>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                    <h2 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>Agenda de Hoje</h2>
                    <div className="space-y-3">
                        {loading ? (
                            <div className={`text-center py-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
                                <p>Carregando...</p>
                            </div>
                        ) : commitments.filter(c => c.date === new Date().toISOString().split('T')[0]).map(commitment => (
                            <div key={commitment.id} className={`flex items-center justify-between p-3 ${darkMode ? 'bg-gray-700' : 'bg-gray-50'} rounded-lg`}>
                                <div>
                                    <h4 className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{commitment.title}</h4>
                                    <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{commitment.time} - {commitment.location}</p>
                                </div>
                                <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(commitment.status)}`}>
                                    {commitment.status}
                                </span>
                            </div>
                        ))}
                    </div>
                </div>

                <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border`}>
                    <h2 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>Demandas por Área</h2>
                    <div className="space-y-3">
                        {Object.entries(analytics.requestsByArea).map(([area, count]) => (
                            <div key={area} className="flex items-center justify-between">
                                <span className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{area}</span>
                                <div className="flex items-center space-x-2">
                                    <div className={`w-20 ${darkMode ? 'bg-gray-700' : 'bg-gray-200'} rounded-full h-2`}>
                                        <div className="bg-blue-600 h-2 rounded-full" style={{ width: `${(count / 100) * 100}%` }}></div>
                                    </div>
                                    <span className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-900'}`}>{count}</span>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
            
            {/* Insights Inteligentes */}
            <SmartInsights 
                darkMode={darkMode}
                analytics={analytics}
                citizens={citizens || []}
                requests={requests || []}
                commitments={commitments}
            />
        </div>
    );
};
