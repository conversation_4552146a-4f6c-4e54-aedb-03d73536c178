import { supabase } from '@/lib/supabase'
import { Database } from '@/types/database'

type Commitment = Database['public']['Tables']['commitments']['Row']
type CommitmentInsert = Database['public']['Tables']['commitments']['Insert']
type CommitmentUpdate = Database['public']['Tables']['commitments']['Update']

export const commitmentService = {
  async getAll() {
    const { data, error } = await supabase
      .from('commitments')
      .select('*')
      .order('date', { ascending: true })
    
    if (error) throw error
    return data
  },

  async create(commitment: CommitmentInsert) {
    const { data, error } = await supabase
      .from('commitments')
      .insert(commitment)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async update(id: number, updates: CommitmentUpdate) {
    const { data, error } = await supabase
      .from('commitments')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  },

  async delete(id: number) {
    const { error } = await supabase
      .from('commitments')
      .delete()
      .eq('id', id)
    
    if (error) throw error
  }
}