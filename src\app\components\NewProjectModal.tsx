"use client";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/app/components/ui/dialog";

interface NewProjectModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  darkMode: boolean;
}

export const NewProjectModal = ({ isOpen, setIsOpen, darkMode }: NewProjectModalProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white'}`}>
        <DialogHeader>
          <DialogTitle className={`${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
            Novo Projeto
          </DialogTitle>
        </DialogHeader>
        <div className="p-4">
          <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Formulário para criar novo projeto será implementado aqui.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};