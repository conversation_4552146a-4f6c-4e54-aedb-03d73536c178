"use client";
import { X, Clock, MapPin, User, Briefcase, FileText } from 'lucide-react';

interface Commitment {
  id: number;
  title: string;
  description?: string;
  date: string;
  time: string;
  status: string;
  location?: string;
  attendees?: string[];
  projectId?: number;
  projectName?: string;
  demandId?: number;
  demandTitle?: string;
}

interface DayCommitmentsModalProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  dayData: {date: string, commitments: Commitment[]} | null;
  darkMode: boolean;
  setSelectedCommitment: (commitment: Commitment) => void;
}

export const DayCommitmentsModal = ({ isOpen, setIsOpen, dayData, darkMode, setSelectedCommitment }: DayCommitmentsModalProps) => {
  if (!isOpen || !dayData) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmado': return 'bg-green-100 text-green-800 border-green-200';
      case 'pendente': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'cancelado': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className={`max-w-2xl w-full max-h-[90vh] overflow-y-auto rounded-lg ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white'} border p-6`}>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className={`text-xl font-semibold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
              Compromissos do Dia
            </h2>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {formatDate(dayData.date)}
            </p>
          </div>
          <button 
            onClick={() => setIsOpen(false)} 
            className={`p-2 rounded-full ${darkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-600'}`}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="space-y-4">
          {(dayData?.commitments || []).map(commitment => (
            <div
              key={commitment.id}
              onClick={() => {
                setSelectedCommitment(commitment);
                setIsOpen(false);
              }}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                darkMode ? 'border-gray-700 hover:bg-gray-700' : 'border-gray-200 hover:bg-gray-50'
              }`}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h3 className={`font-medium ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
                    {commitment.title}
                  </h3>
                  {commitment.description && (
                    <p className={`text-sm mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {commitment.description}
                    </p>
                  )}
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(commitment.status)}`}>
                  {commitment.status}
                </span>
              </div>

              <div className={`grid grid-cols-1 md:grid-cols-2 gap-3 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4" />
                  <span>{commitment.time}</span>
                </div>
                
                {commitment.location && (
                  <div className="flex items-center space-x-2">
                    <MapPin className="w-4 h-4" />
                    <span className="truncate">{commitment.location}</span>
                  </div>
                )}

                {commitment.attendees && commitment.attendees.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4" />
                    <span className="truncate">{commitment.attendees.join(', ')}</span>
                  </div>
                )}

                {commitment.projectName && (
                  <div className="flex items-center space-x-2">
                    <Briefcase className="w-4 h-4" />
                    <span className="truncate">Projeto: {commitment.projectName}</span>
                  </div>
                )}

                {commitment.demandTitle && (
                  <div className="flex items-center space-x-2 md:col-span-2">
                    <FileText className="w-4 h-4" />
                    <span className="truncate">Demanda: {commitment.demandTitle}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 text-center">
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {dayData?.commitments?.length || 0} compromisso{(dayData?.commitments?.length || 0) !== 1 ? 's' : ''} encontrado{(dayData?.commitments?.length || 0) !== 1 ? 's' : ''}
          </p>
        </div>
      </div>
    </div>
  );
};