# Como usar a API Multi-tenant

## 1. Configuração inicial

Execute o script SQL no Supabase:
```sql
-- <PERSON> o conteúdo de supabase-setup.sql
```

## 2. Obter API Key do tenant

```sql
-- No Supabase, execute para obter a API Key:
select t.name, t.slug, k.api_key 
from tenants t 
join tenant_api_keys k on t.id = k.tenant_id 
where t.slug = 'gabinete-exemplo';
```

### Ou criar novo tenant via API:

```javascript
// POST /api/admin/tenants
const response = await fetch('/api/admin/tenants', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'Gabinete São Paulo',
    slug: 'gabinete-sp'
  })
});

const { tenant, api_key } = await response.json();
console.log('API Key:', api_key);
```

## 3. Fazer requisição

```javascript
const response = await fetch('/api/ai', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer SUA_API_KEY_AQUI',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    page: 'propostas',
    messages: [
      {
        role: 'user', 
        content: 'Quais temas devo priorizar para educação?'
      }
    ]
  })
});

const data = await response.json();
console.log(data.response);
```

## 4. Resposta esperada

```json
{
  "response": "Com base no contexto do seu gabinete, sugiro priorizar..."
}
```