import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

export async function GET(request: NextRequest) {
  try {
    console.log('Testando conexão com Supabase...');
    console.log('URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
    console.log('ANON KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Presente' : 'Ausente');
    
    // Teste simples de conexão
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Erro na conexão:', error);
      return NextResponse.json({ 
        success: false, 
        error: error.message,
        config: {
          url: process.env.NEXT_PUBLIC_SUPABASE_URL,
          hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
        }
      }, { status: 500 });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Conexão com Supabase OK',
      session: data.session ? 'Sessão ativa' : 'Sem sessão',
      config: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      }
    });
    
  } catch (error) {
    console.error('Erro no teste:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Erro interno',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    }, { status: 500 });
  }
}
