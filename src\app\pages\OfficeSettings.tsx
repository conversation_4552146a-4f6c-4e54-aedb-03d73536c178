"use client";
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/app/components/ui/tabs";
import { Building, Users, Upload, Save, Plus, Edit, Trash2, Camera, Search } from 'lucide-react';
import { officeSettingsService, OfficeSettings } from '@/services/officeSettingsService';
import { userService, User } from '@/services/userService';
import { cepService } from '@/services/cepService';

export const OfficeSettings = ({ darkMode }: { darkMode: boolean }) => {
  const [settings, setSettings] = useState<OfficeSettings | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isUserModalOpen, setIsUserModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [loadingCEP, setLoadingCEP] = useState(false);
  const [cepValue, setCepValue] = useState('');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [settingsData, usersData] = await Promise.all([
        officeSettingsService.get(),
        userService.getAll()
      ]);
      setSettings(settingsData);
      setUsers(usersData || []);
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async (formData: FormData) => {
    setSaving(true);
    try {
      const updatedSettings = {
        office_name: formData.get('office_name') as string,
        office_type: formData.get('office_type') as string,
        holder_name: formData.get('holder_name') as string,
        institution_name: formData.get('institution_name') as string,
        city: formData.get('city') as string,
        state: formData.get('state') as string,
        address: formData.get('address') as string,
        phone: formData.get('phone') as string,
        email: formData.get('email') as string,
        website: formData.get('website') as string,
      };

      const result = await officeSettingsService.update(updatedSettings);
      setSettings(result);
      alert('Configurações salvas com sucesso!');
    } catch (error) {
      console.error('Erro ao salvar:', error);
      alert('Erro ao salvar configurações');
    } finally {
      setSaving(false);
    }
  };

  const handleLogoUpload = async (file: File) => {
    try {
      const logoUrl = await officeSettingsService.uploadLogo(file);
      const result = await officeSettingsService.update({ logo_url: logoUrl });
      setSettings(result);
      alert('Logo atualizado com sucesso!');
    } catch (error) {
      console.error('Erro ao fazer upload:', error);
      alert('Erro ao fazer upload do logo');
    }
  };

  const handleCoatOfArmsUpload = async (file: File) => {
    try {
      const coatUrl = await officeSettingsService.uploadCoatOfArms(file);
      const result = await officeSettingsService.update({ coat_of_arms_url: coatUrl });
      setSettings(result);
      alert('Brasão atualizado com sucesso!');
    } catch (error) {
      console.error('Erro ao fazer upload:', error);
      alert('Erro ao fazer upload do brasão');
    }
  };

  const handleSaveUser = async (formData: FormData) => {
    try {
      const userData = {
        email: formData.get('email') as string,
        name: formData.get('name') as string,
        role: formData.get('role') as any,
        phone: formData.get('phone') as string,
        permissions: {},
        is_active: true
      };

      if (editingUser) {
        await userService.update(editingUser.id, userData);
      } else {
        await userService.create(userData);
      }

      await loadData();
      setIsUserModalOpen(false);
      setEditingUser(null);
      alert('Usuário salvo com sucesso!');
    } catch (error) {
      console.error('Erro ao salvar usuário:', error);
      alert('Erro ao salvar usuário');
    }
  };

  if (loading) {
    return (
      <div className={`text-center py-12 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p>Carregando configurações...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className={`text-2xl font-bold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
            Configurações do Gabinete
          </h1>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Configure as informações do seu gabinete e gerencie sua equipe
          </p>
        </div>
      </div>

      <Tabs defaultValue="office" className="w-full">
        <TabsList className={`grid w-full grid-cols-2 ${darkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100'}`}>
          <TabsTrigger value="office" className="flex items-center space-x-2">
            <Building className="w-4 h-4" />
            <span>Gabinete</span>
          </TabsTrigger>
          <TabsTrigger value="team" className="flex items-center space-x-2">
            <Users className="w-4 h-4" />
            <span>Equipe</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="office" className="space-y-6">
          <form onSubmit={(e) => {
            e.preventDefault();
            handleSaveSettings(new FormData(e.currentTarget));
          }}>
            <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow-sm border space-y-6`}>
              
              {/* Informações Básicas */}
              <div>
                <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                  Informações Básicas
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Nome do Gabinete
                    </label>
                    <input
                      name="office_name"
                      defaultValue={settings?.office_name}
                      className={`w-full px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                      placeholder="Ex: Gabinete do Vereador João"
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Tipo de Cargo
                    </label>
                    <select
                      name="office_type"
                      defaultValue={settings?.office_type}
                      className={`w-full px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                    >
                      <option value="vereador">Vereador</option>
                      <option value="prefeito">Prefeito</option>
                      <option value="deputado_estadual">Deputado Estadual</option>
                      <option value="deputado_federal">Deputado Federal</option>
                      <option value="senador">Senador</option>
                    </select>
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Nome do Titular
                    </label>
                    <input
                      name="holder_name"
                      defaultValue={settings?.holder_name}
                      className={`w-full px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                      placeholder="Nome completo do titular"
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Instituição
                    </label>
                    <input
                      name="institution_name"
                      defaultValue={settings?.institution_name}
                      className={`w-full px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                      placeholder="Ex: Câmara Municipal de São Paulo"
                    />
                  </div>
                </div>
              </div>

              {/* Localização */}
              <div>
                <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                  Localização
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Cidade
                    </label>
                    <input
                      name="city"
                      defaultValue={settings?.city}
                      className={`w-full px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Estado
                    </label>
                    <input
                      name="state"
                      defaultValue={settings?.state}
                      className={`w-full px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      CEP
                    </label>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={cepValue}
                        onChange={(e) => setCepValue(e.target.value)}
                        className={`flex-1 px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                        placeholder="00000-000"
                      />
                      <button
                        type="button"
                        onClick={async () => {
                          if (!cepValue) return;
                          setLoadingCEP(true);
                          const cepData = await cepService.buscarCEP(cepValue);
                          if (cepData) {
                            const form = document.querySelector('form') as HTMLFormElement;
                            if (form) {
                              (form.querySelector('[name="address"]') as HTMLInputElement).value = `${cepData.logradouro}, ${cepData.bairro}`;
                              (form.querySelector('[name="city"]') as HTMLInputElement).value = cepData.localidade;
                              (form.querySelector('[name="state"]') as HTMLInputElement).value = cepData.uf;
                            }
                            setCepValue(cepService.formatCEP(cepData.cep));
                          }
                          setLoadingCEP(false);
                        }}
                        disabled={loadingCEP || !cepValue}
                        className={`px-3 py-2 rounded-lg ${darkMode ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-blue-600 hover:bg-blue-700 text-white'} disabled:opacity-50`}
                      >
                        {loadingCEP ? '...' : <Search className="w-4 h-4" />}
                      </button>
                    </div>
                  </div>
                  <div className="md:col-span-2">
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Endereço Completo
                    </label>
                    <input
                      name="address"
                      defaultValue={settings?.address}
                      className={`w-full px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                    />
                  </div>
                </div>
              </div>

              {/* Contato */}
              <div>
                <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                  Contato
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Telefone
                    </label>
                    <input
                      name="phone"
                      defaultValue={settings?.phone}
                      className={`w-full px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Email
                    </label>
                    <input
                      name="email"
                      type="email"
                      defaultValue={settings?.email}
                      className={`w-full px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Website
                    </label>
                    <input
                      name="website"
                      defaultValue={settings?.website}
                      className={`w-full px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                    />
                  </div>
                </div>
              </div>

              {/* Imagens */}
              <div>
                <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
                  Logotipo e Brasão
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Logotipo
                    </label>
                    <div className={`border-2 border-dashed rounded-lg p-6 text-center ${darkMode ? 'border-gray-600' : 'border-gray-300'}`}>
                      {settings?.logo_url ? (
                        <img src={settings.logo_url} alt="Logo" className="w-24 h-24 mx-auto mb-4 object-contain" />
                      ) : (
                        <Camera className={`w-12 h-12 mx-auto mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                      )}
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => e.target.files?.[0] && handleLogoUpload(e.target.files[0])}
                        className="hidden"
                        id="logo-upload"
                      />
                      <label
                        htmlFor="logo-upload"
                        className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg cursor-pointer hover:bg-blue-700"
                      >
                        <Upload className="w-4 h-4" />
                        <span>Escolher Logo</span>
                      </label>
                    </div>
                  </div>
                  
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Brasão
                    </label>
                    <div className={`border-2 border-dashed rounded-lg p-6 text-center ${darkMode ? 'border-gray-600' : 'border-gray-300'}`}>
                      {settings?.coat_of_arms_url ? (
                        <img src={settings.coat_of_arms_url} alt="Brasão" className="w-24 h-24 mx-auto mb-4 object-contain" />
                      ) : (
                        <Camera className={`w-12 h-12 mx-auto mb-4 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
                      )}
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => e.target.files?.[0] && handleCoatOfArmsUpload(e.target.files[0])}
                        className="hidden"
                        id="coat-upload"
                      />
                      <label
                        htmlFor="coat-upload"
                        className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg cursor-pointer hover:bg-blue-700"
                      >
                        <Upload className="w-4 h-4" />
                        <span>Escolher Brasão</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={saving}
                  className="inline-flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  <Save className="w-4 h-4" />
                  <span>{saving ? 'Salvando...' : 'Salvar Configurações'}</span>
                </button>
              </div>
            </div>
          </form>
        </TabsContent>

        <TabsContent value="team" className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className={`text-lg font-semibold ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
              Equipe do Gabinete
            </h3>
            <button
              onClick={() => setIsUserModalOpen(true)}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Plus className="w-4 h-4" />
              <span>Adicionar Membro</span>
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {users.map(user => (
              <div key={user.id} className={`${darkMode ? 'bg-gray-800' : 'bg-white'} p-4 rounded-lg shadow-sm border`}>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    {user.avatar_url ? (
                      <img src={user.avatar_url} alt={user.name} className="w-10 h-10 rounded-full" />
                    ) : (
                      <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                        {user.name.charAt(0)}
                      </div>
                    )}
                    <div>
                      <h4 className={`font-medium ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>{user.name}</h4>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>{user.role}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => {
                        setEditingUser(user);
                        setIsUserModalOpen(true);
                      }}
                      className="p-1 text-blue-600 hover:bg-blue-100 rounded"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => userService.delete(user.id).then(loadData)}
                      className="p-1 text-red-600 hover:bg-red-100 rounded"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  <p>{user.email}</p>
                  {user.phone && <p>{user.phone}</p>}
                </div>
              </div>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* Modal de Usuário */}
      {isUserModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-6 w-full max-w-md`}>
            <h3 className={`text-lg font-semibold mb-4 ${darkMode ? 'text-gray-200' : 'text-gray-900'}`}>
              {editingUser ? 'Editar Membro' : 'Adicionar Membro'}
            </h3>
            <form onSubmit={(e) => {
              e.preventDefault();
              handleSaveUser(new FormData(e.currentTarget));
            }}>
              <div className="space-y-4">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Nome
                  </label>
                  <input
                    name="name"
                    defaultValue={editingUser?.name}
                    required
                    className={`w-full px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                  />
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Email
                  </label>
                  <input
                    name="email"
                    type="email"
                    defaultValue={editingUser?.email}
                    required
                    className={`w-full px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                  />
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Cargo
                  </label>
                  <select
                    name="role"
                    defaultValue={editingUser?.role}
                    required
                    className={`w-full px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                  >
                    <option value="titular">Titular</option>
                    <option value="assessor_principal">Assessor Principal</option>
                    <option value="assessor_marketing">Assessor de Marketing</option>
                    <option value="assessor">Assessor</option>
                  </select>
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    Telefone
                  </label>
                  <input
                    name="phone"
                    defaultValue={editingUser?.phone}
                    className={`w-full px-3 py-2 border rounded-lg ${darkMode ? 'bg-gray-700 border-gray-600 text-gray-100' : 'bg-white border-gray-300 text-gray-900'}`}
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setIsUserModalOpen(false);
                    setEditingUser(null);
                  }}
                  className={`px-4 py-2 border rounded-lg ${darkMode ? 'border-gray-600 text-gray-300 hover:bg-gray-700' : 'border-gray-300 text-gray-700 hover:bg-gray-50'}`}
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Salvar
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};